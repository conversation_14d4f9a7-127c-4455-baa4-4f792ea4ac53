/* 现代化控制台样式 - 极简黑白配色 */
:root {
    /* 主色调系统 - 纯黑白设计 */
    --color-primary: #000000;
    --color-secondary: #1a1a1a;
    --color-tertiary: #333333;
    --color-quaternary: #4a4a4a;
    --color-accent: #666666;

    /* 背景色系统 - 纯净白色 */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f0f0f0;
    --bg-quaternary: #e8e8e8;
    --bg-dark: #000000;
    --bg-dark-secondary: #1a1a1a;
    --bg-dark-tertiary: #2a2a2a;

    /* 边框色系统 - 精细层次 */
    --border-light: #e0e0e0;
    --border-medium: #c0c0c0;
    --border-dark: #000000;
    --border-subtle: #f0f0f0;

    /* 文字色系统 - 清晰对比 */
    --text-primary: #000000;
    --text-secondary: #333333;
    --text-tertiary: #666666;
    --text-quaternary: #999999;
    --text-muted: #cccccc;
    --text-inverse: #ffffff;

    /* 状态颜色 - 黑白主题 */
    --color-success: #000000;
    --color-warning: #333333;
    --color-danger: #000000;
    --color-info: #666666;

    /* 阴影系统 - 极简微阴影 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
    --shadow-md: 0 2px 6px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-xl: 0 8px 24px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 16px 48px rgba(0, 0, 0, 0.12);

    /* 边框半径 - 现代几何 */
    --radius-none: 0px;
    --radius-xs: 2px;
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    --radius-3xl: 24px;

    /* 间距系统 - 精确控制 */
    --space-0: 0px;
    --space-1: 2px;
    --space-2: 4px;
    --space-3: 6px;
    --space-4: 8px;
    --space-5: 10px;
    --space-6: 12px;
    --space-8: 16px;
    --space-10: 20px;
    --space-12: 24px;
    --space-16: 32px;
    --space-20: 40px;
    --space-24: 48px;

    /* 字体系统 - 现代无衬线 */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, monospace;

    /* 字体大小 - 精确层级 */
    --text-2xs: 0.625rem;
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;

    /* 字体权重 - 精细控制 */
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* 行高系统 */
    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* 动画系统 - 现代化缓动 */
    --transition-none: 0s;
    --transition-fast: 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slower: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* 布局系统 */
    --sidebar-width: 260px;
    --sidebar-width-collapsed: 60px;
    --header-height: 60px;
    --content-max-width: 1400px;

    /* Z-index 层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* 全局重置 - 现代化基础 */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 现代化滚动条 - 极简设计 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-sm);
    transition: background var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-tertiary);
}

::-webkit-scrollbar-corner {
    background: var(--bg-tertiary);
}

/* Firefox 滚动条 */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--border-medium) var(--bg-tertiary);
}

/* 主布局容器 - 极简设计 */
.dashboard {
    display: flex;
    width: 100%;
    min-height: 100vh;
    background: var(--bg-primary);
    position: relative;
}

/* 现代化侧边栏 - 极简黑色设计 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-dark);
    color: var(--text-inverse);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: var(--z-fixed);
    transition: all var(--transition-base);
    border-right: 1px solid var(--bg-dark-secondary);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: var(--space-8) var(--space-6);
    border-bottom: 1px solid var(--bg-dark-secondary);
    background: var(--bg-dark);
    position: relative;
    flex-shrink: 0;
}

.sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--space-6);
    right: var(--space-6);
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%
    );
}

.sidebar-header h2 {
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-2);
    letter-spacing: -0.025em;
    color: var(--text-inverse);
    line-height: var(--line-height-tight);
}

.sidebar-header p {
    font-size: var(--text-xs);
    color: rgba(255, 255, 255, 0.6);
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.025em;
    line-height: var(--line-height-normal);
    text-transform: uppercase;
}

/* 现代化导航样式 - 极简设计 */
.sidebar-nav {
    flex: 1;
    padding: var(--space-6) 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0 var(--space-4);
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: var(--space-1);
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--text-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
    position: relative;
    margin: 0;
    line-height: var(--line-height-snug);
    border: 1px solid transparent;
}

.sidebar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 0;
    background: var(--text-inverse);
    transition: height var(--transition-base);
    border-radius: 0 var(--radius-xs) var(--radius-xs) 0;
}

.sidebar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-inverse);
    font-weight: var(--font-weight-semibold);
    border-color: rgba(255, 255, 255, 0.2);
}

.sidebar-nav .nav-link.active::before {
    height: 60%;
}

/* 现代化侧边栏图标 - 精简设计 */
.nav-icon {
    width: 18px;
    height: 18px;
    margin-right: var(--space-3);
    opacity: 0.7;
    transition: all var(--transition-base);
    stroke-width: 1.5;
    flex-shrink: 0;
}

.nav-link:hover .nav-icon {
    opacity: 0.9;
}

.nav-link.active .nav-icon {
    opacity: 1;
}

/* 现代化侧边栏底部 - 极简设计 */
.sidebar-footer {
    padding: var(--space-6);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
    text-align: center;
    background: var(--bg-dark);
    flex-shrink: 0;
}

.sidebar-footer p {
    font-size: var(--text-2xs);
    color: rgba(255, 255, 255, 0.4);
    margin: 0;
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

/* 现代化主内容区域 - 极简布局 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: var(--bg-primary);
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 现代化内容头部 - 极简设计 */
.content-header {
    background: var(--bg-primary);
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.95);
}

.content-header h1 {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    letter-spacing: -0.025em;
    margin: 0;
    line-height: var(--line-height-tight);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    color: var(--text-secondary);
}

.user-info span {
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

/* 现代化内容主体 - 极简布局 */
.content-body {
    flex: 1;
    padding: var(--space-8);
    max-width: var(--content-max-width);
    margin: 0 auto;
    width: 100%;
}

/* 现代化卡片样式 - 极简设计 */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-8);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
    overflow: hidden;
    position: relative;
}

.card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--border-medium);
}

.card-header {
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
    position: relative;
}

.card-header h2 {
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.025em;
    line-height: var(--line-height-tight);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex-wrap: wrap;
}

.card-body {
    padding: var(--space-8);
    line-height: var(--line-height-normal);
}

/* 现代化统计网格 - 响应式设计 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-12);
}

/* 现代化统计卡片 - 极简设计 */
.stat-card {
    background: var(--bg-primary);
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--color-primary);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-medium);
}

.stat-card h3 {
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.stat-card .stat-value {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
    letter-spacing: -0.025em;
    line-height: var(--line-height-none);
}

.stat-card .stat-label {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    line-height: var(--line-height-snug);
    font-weight: var(--font-weight-medium);
}

/* 现代化按钮样式 - 极简设计 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    text-decoration: none;
    cursor: pointer;
    gap: var(--space-2);
    position: relative;
    transition: all var(--transition-base);
    letter-spacing: 0.025em;
    line-height: var(--line-height-none);
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.btn-primary {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--color-secondary);
    border-color: var(--color-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-medium);
}

.btn-secondary:hover {
    background: var(--bg-quaternary);
    border-color: var(--border-dark);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--border-medium);
}

.btn-outline:hover {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
}

/* 现代化表单样式 - 极简设计 */
.form-group {
    margin-bottom: var(--space-6);
    position: relative;
}

.form-group label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    letter-spacing: 0.025em;
    line-height: var(--line-height-snug);
}

.form-control {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    background: var(--bg-primary);
    transition: all var(--transition-base);
    font-family: var(--font-sans);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    background: var(--bg-primary);
}

.form-control::placeholder {
    color: var(--text-quaternary);
    font-style: normal;
}

.form-control:disabled {
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
    cursor: not-allowed;
}

.form-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-start;
    margin-top: var(--space-6);
    flex-wrap: wrap;
    align-items: center;
}

/* 现代化表格样式 - 极简设计 */
.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    background: var(--bg-primary);
    margin-bottom: var(--space-6);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    font-size: var(--text-sm);
}

.table th,
.table td {
    padding: var(--space-3) var(--space-4);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
    line-height: var(--line-height-snug);
}

.table th {
    background: var(--bg-dark);
    font-weight: var(--font-weight-bold);
    color: var(--text-inverse);
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    border-bottom: 1px solid var(--bg-dark-secondary);
}

.table tbody tr {
    transition: background-color var(--transition-fast);
    border-bottom: 1px solid var(--border-subtle);
}

.table tbody tr:last-child {
    border-bottom: none;
}

.table tbody tr:hover {
    background: var(--bg-secondary);
}

.table tbody tr:nth-child(even) {
    background: var(--bg-primary);
}

.table tbody tr:nth-child(odd) {
    background: var(--bg-primary);
}

/* 现代化徽章样式 - 极简设计 */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-sm);
    letter-spacing: 0.025em;
    border: 1px solid transparent;
    transition: all var(--transition-fast);
    min-width: 60px;
    text-align: center;
    line-height: var(--line-height-none);
    white-space: nowrap;
}

.badge-success {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
}

.badge-warning {
    background: var(--bg-quaternary);
    color: var(--text-primary);
    border-color: var(--border-medium);
}

.badge-danger {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
}

.badge-info {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-color: var(--border-light);
}

.badge-dark {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
}

.badge-light {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-light);
}

/* 现代化分页样式 - 极简设计 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-1);
    margin: var(--space-6) 0;
}

.pagination .page-btn {
    padding: var(--space-2) var(--space-3);
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-base);
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: var(--line-height-none);
}

.pagination .page-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--border-medium);
}

.pagination .page-btn.active {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
    font-weight: var(--font-weight-semibold);
}

.pagination .page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--bg-tertiary);
    color: var(--text-quaternary);
}

.pagination .page-btn:disabled:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-light);
}

/* 现代化模态框样式 - 极简设计 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-modal);
    backdrop-filter: blur(4px);
    animation: modalFadeIn var(--transition-base) ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--border-light);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn var(--transition-base) ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.modal-close {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    font-size: var(--text-lg);
    cursor: pointer;
    color: var(--text-primary);
    padding: var(--space-2);
    border-radius: var(--radius-md);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    line-height: var(--line-height-none);
}

.modal-close:hover {
    background: var(--bg-quaternary);
    border-color: var(--border-medium);
}

.modal-body {
    padding: var(--space-8);
}

/* 现代化加载动画 - 极简设计 */
.loader {
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 0.8s linear infinite;
    margin: var(--space-6) auto;
}

/* 现代化旋转器样式 */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spin 0.75s linear infinite;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.1em;
}

/* 现代化动画关键帧 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 内容区域显示 */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

.content-section.active {
    display: block;
}

/* 现代化响应式设计 - 移动优先 */
@media (max-width: 1400px) {
    .content-body {
        padding: var(--space-6);
    }
}

@media (max-width: 1200px) {
    :root {
        --sidebar-width: 240px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: var(--space-4);
    }
}

@media (max-width: 1024px) {
    :root {
        --sidebar-width: 220px;
    }

    .content-header {
        padding: var(--space-4) var(--space-6);
    }

    .content-body {
        padding: var(--space-4);
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-4);
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-base);
        box-shadow: var(--shadow-2xl);
        z-index: var(--z-modal);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .content-header {
        padding: var(--space-4) var(--space-4);
    }

    .content-header h1 {
        font-size: var(--text-xl);
    }

    .content-body {
        padding: var(--space-4);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-3);
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .card-header {
        padding: var(--space-4) var(--space-6);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }

    .card-body {
        padding: var(--space-6);
    }

    .table th,
    .table td {
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-xs);
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: var(--space-3) var(--space-4);
    }

    .content-header h1 {
        font-size: var(--text-lg);
    }

    .content-body {
        padding: var(--space-3);
    }

    .card-header,
    .card-body {
        padding: var(--space-4);
    }

    .stat-card {
        padding: var(--space-6);
    }

    .stat-card .stat-value {
        font-size: var(--text-2xl);
    }

    .modal-content {
        width: 95%;
        margin: var(--space-4);
    }

    .modal-header,
    .modal-body {
        padding: var(--space-4);
    }

    .btn {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-sm);
    }
}

/* 现代化侧边栏切换按钮 - 极简设计 */
.sidebar-toggle {
    position: fixed;
    top: var(--space-4);
    left: var(--space-4);
    z-index: calc(var(--z-modal) + 1);
    background: var(--color-primary);
    color: var(--text-inverse);
    border: 1px solid var(--color-primary);
    border-radius: var(--radius-md);
    padding: var(--space-3);
    cursor: pointer;
    display: none;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-base);
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: var(--color-secondary);
    border-color: var(--color-secondary);
    transform: scale(1.05);
}

.sidebar-toggle svg {
    width: 20px;
    height: 20px;
}

@media (max-width: 768px) {
    .sidebar-toggle {
        display: flex;
    }
}

/* 现代化空状态 - 极简设计 */
.empty-state {
    text-align: center;
    padding: var(--space-16);
    color: var(--text-tertiary);
}

.empty-state svg {
    width: 48px;
    height: 48px;
    margin-bottom: var(--space-6);
    opacity: 0.3;
    stroke: var(--text-quaternary);
}

.empty-state h3 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-3);
    color: var(--text-secondary);
    font-weight: var(--font-weight-semibold);
}

.empty-state p {
    font-size: var(--text-sm);
    margin-bottom: var(--space-6);
    color: var(--text-tertiary);
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
    line-height: var(--line-height-relaxed);
}

/* 现代化警告框 - 极简设计 */
.alert {
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-6);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-sm);
    line-height: var(--line-height-snug);
}

.alert-success {
    background: rgba(0, 0, 0, 0.02);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

.alert-warning {
    background: rgba(51, 51, 51, 0.02);
    border-color: var(--color-tertiary);
    color: var(--text-primary);
}

.alert-danger {
    background: rgba(0, 0, 0, 0.04);
    border-color: var(--color-primary);
    color: var(--text-primary);
}

.alert-info {
    background: rgba(102, 102, 102, 0.02);
    border-color: var(--color-accent);
    color: var(--text-primary);
}

/* 现代化批量操作工具栏 - 极简设计 */
.batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    padding: var(--space-4) var(--space-6);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex-wrap: wrap;
}

.batch-info {
    color: var(--text-tertiary);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
}

/* 现代化表格操作按钮 - 极简设计 */
.table .actions {
    white-space: nowrap;
}

.table .actions .btn {
    margin-right: var(--space-1);
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
}

.table .actions .btn:last-child {
    margin-right: 0;
}























/* 现代化工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-normal { font-weight: var(--font-weight-normal); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-quaternary); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border { border: 1px solid var(--border-light); }
.border-light { border-color: var(--border-light); }
.border-medium { border-color: var(--border-medium); }
.border-dark { border-color: var(--border-dark); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.transition { transition: all var(--transition-base); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

.hover\:bg-secondary:hover { background-color: var(--bg-secondary); }
.hover\:bg-tertiary:hover { background-color: var(--bg-tertiary); }
.hover\:text-primary:hover { color: var(--text-primary); }
.hover\:border-medium:hover { border-color: var(--border-medium); }

.focus\:outline-none:focus { outline: none; }
.focus\:ring:focus { box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1); }

/* 选择文本样式 */
::selection {
    background-color: var(--color-primary);
    color: var(--text-inverse);
}

/* 焦点样式 */
*:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* 禁用状态 */
.disabled,
[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 现代化代码块 */
.code-display {
    font-family: var(--font-mono);
    font-weight: var(--font-weight-semibold);
    letter-spacing: 0.05em;
    background: var(--bg-tertiary);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    font-size: var(--text-sm);
    display: inline-block;
}

/* 现代化复选框 */
.code-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--color-primary);
    border-radius: var(--radius-xs);
}

.code-checkbox:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 现代化Toast通知 */
.toast {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    color: var(--text-inverse);
    font-weight: var(--font-weight-medium);
    z-index: var(--z-tooltip);
    box-shadow: var(--shadow-xl);
    max-width: 400px;
    word-wrap: break-word;
    animation: slideIn var(--transition-base) ease-out;
    border: 1px solid transparent;
    font-size: var(--text-sm);
}

.toast-success {
    background: var(--color-primary);
    border-color: var(--color-primary);
}

.toast-error {
    background: var(--color-primary);
    border-color: var(--color-primary);
}

.toast-warning {
    background: var(--color-tertiary);
    border-color: var(--color-tertiary);
}

.toast-info {
    background: var(--color-accent);
    border-color: var(--color-accent);
}