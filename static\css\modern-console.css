/* 现代化控制台样式 - 黑白配色现代风格 */
:root {
    /* 主色调系统 - 现代黑白 */
    --color-primary: #000000;
    --color-secondary: #1a1a1a;
    --color-tertiary: #2d2d2d;
    --color-quaternary: #404040;
    --color-accent: #666666;

    /* 背景色系统 */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    --bg-dark: #000000;
    --bg-dark-secondary: #1a1a1a;

    /* 边框色系统 */
    --border-light: #e5e5e5;
    --border-medium: #d0d0d0;
    --border-dark: #000000;

    /* 文字色系统 */
    --text-primary: #000000;
    --text-secondary: #404040;
    --text-tertiary: #666666;
    --text-muted: #999999;
    --text-inverse: #ffffff;

    /* 状态颜色 - 现代化 */
    --color-success: #000000;
    --color-warning: #404040;
    --color-danger: #000000;
    --color-info: #666666;

    /* 阴影系统 - 现代化微阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.12);

    /* 边框半径 - 现代化 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    --radius-full: 9999px;

    /* 间距系统 - 紧凑型设计 */
    --space-xs: 2px;
    --space-sm: 4px;
    --space-md: 8px;
    --space-lg: 12px;
    --space-xl: 16px;
    --space-2xl: 20px;
    --space-3xl: 24px;
    --space-4xl: 32px;

    /* 字体系统 */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

    /* 字体大小 - 紧凑型设计 */
    --text-xs: 0.6rem;
    --text-sm: 0.75rem;
    --text-base: 0.875rem;
    --text-lg: 1rem;
    --text-xl: 1.125rem;
    --text-2xl: 1.25rem;
    --text-3xl: 1.5rem;
    --text-4xl: 1.875rem;

    /* 字体权重 */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* 行高系统 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* 动画系统 - 现代化 */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 布局 */
    --sidebar-width: 280px;
    --header-height: 64px;

    /* 动画关键帧 */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
}

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 100%;
    height: 100%;
}

body {
    font-family: var(--font-sans);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    background: transparent;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: transparent;
}

::-webkit-scrollbar-thumb:hover {
    background: transparent;
}

/* 为Firefox隐藏滚动条 */
* {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* 主布局 */
.dashboard {
    display: flex;
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

/* 现代化侧边栏 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-dark);
    color: var(--text-inverse);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform var(--transition-base);
    border-right: 1px solid var(--border-medium);
    backdrop-filter: blur(10px);
}

.sidebar-header {
    padding: var(--space-xl) var(--space-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: var(--bg-dark);
    position: relative;
}

.sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--space-xl);
    right: var(--space-xl);
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

.sidebar-header h2 {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-sm);
    letter-spacing: -0.02em;
    color: var(--text-inverse);
    line-height: var(--line-height-tight);
}

.sidebar-header p {
    font-size: var(--text-sm);
    color: rgba(255, 255, 255, 0.7);
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.02em;
    line-height: var(--line-height-normal);
}

/* 现代化导航样式 */
.sidebar-nav {
    padding: var(--space-lg) 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0 var(--space-lg);
}

.sidebar-nav li {
    margin-bottom: var(--space-sm);
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--text-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
    position: relative;
    margin: var(--space-xs) 0;
    overflow: hidden;
    line-height: var(--line-height-normal);
}

.sidebar-nav .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--text-inverse);
    transform: scaleY(0);
    transition: transform var(--transition-base);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.sidebar-nav .nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    opacity: 0;
    transition: opacity var(--transition-base);
    border-radius: var(--radius-lg);
}

.sidebar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-inverse);
    font-weight: var(--font-weight-semibold);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-link.active::before {
    transform: scaleY(1);
}

.sidebar-nav .nav-link.active::after {
    opacity: 1;
}

/* 现代化侧边栏图标 - 增强版 */
.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--space-lg);
    opacity: 0.8;
    transition: all var(--transition-base);
    stroke-width: 1.5;
    flex-shrink: 0;
}

.nav-link.active .nav-icon {
    opacity: 1;
    transform: scale(1.05);
}

/* 现代化侧边栏底部 */
.sidebar-footer {
    padding: var(--space-xl);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    margin-top: auto;
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
}

.sidebar-footer p {
    font-size: var(--text-xs);
    color: rgba(255, 255, 255, 0.4);
    margin: 0;
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* 现代化主内容区域 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: var(--bg-secondary);
    position: relative;
    width: calc(100% - var(--sidebar-width));
}

/* 现代化内容头部 */
.content-header {
    background: var(--bg-primary);
    padding: var(--space-xl) var(--space-2xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.content-header h1 {
    font-size: var(--text-2xl);
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.02em;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    color: var(--text-secondary);
}

.user-info span {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
}

/* 现代化内容主体 */
.content-body {
    padding: var(--space-xl);
    width: 100%;
}

/* 现代化卡片样式 - 紧凑版 */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-xl);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.card-header {
    padding: var(--space-lg) var(--space-xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
    position: relative;
}

.card-header h2 {
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
    line-height: var(--line-height-tight);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.card-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.card-body {
    padding: var(--space-xl);
    line-height: var(--line-height-normal);
}

/* 现代化统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-3xl);
}

/* 现代化统计卡片 */
.stat-card {
    background: var(--bg-primary);
    padding: var(--space-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.stat-card h3 {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.stat-card .stat-value {
    font-size: var(--text-4xl);
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: var(--space-lg);
    letter-spacing: -0.02em;
    line-height: 1;
}

.stat-card .stat-label {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    line-height: 1.4;
    font-weight: 500;
}

/* 现代化按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-xs);
    font-weight: 600;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    text-decoration: none;
    cursor: pointer;
    gap: var(--space-xs);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
    letter-spacing: 0.02em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    color: var(--text-inverse);
    border: 1px solid var(--color-primary);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.btn-secondary:hover {
    background: var(--border-medium);
    transform: translateY(-1px);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-dark);
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: var(--text-inverse);
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--text-base);
}

/* 现代化表单样式 - 紧凑版 */
.form-group {
    margin-bottom: var(--space-lg);
    position: relative;
}

.form-group label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: 0.02em;
    line-height: var(--line-height-tight);
}

.form-control {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    background-color: var(--bg-primary);
    transition: all var(--transition-base);
    font-family: var(--font-sans);
    line-height: var(--line-height-normal);
    box-shadow: var(--shadow-sm);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.08), var(--shadow-md);
    transform: translateY(-1px);
    background-color: var(--bg-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.form-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: flex-start;
    margin-top: var(--space-lg);
    flex-wrap: wrap;
    align-items: center;
}

/* 现代化表格样式 */
.table-container {
    overflow-x: auto;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    background: var(--bg-primary);
    /* 隐藏滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.table-container::-webkit-scrollbar {
    display: none;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th,
.table td {
    padding: var(--space-sm) var(--space-md);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
    font-size: var(--text-sm);
    transition: background-color var(--transition-fast);
    vertical-align: middle;
}

.table th {
    background: var(--bg-dark);
    font-weight: var(--font-weight-bold);
    color: var(--text-inverse);
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: var(--space-md) var(--space-lg);
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid var(--color-primary);
}

.table tbody tr {
    transition: all var(--transition-fast);
    border-bottom: 1px solid var(--border-light);
}

.table tbody tr:last-child {
    border-bottom: none;
}

.table tbody tr:nth-child(even) {
    background-color: var(--bg-tertiary);
}

.table tbody tr:nth-child(odd) {
    background-color: var(--bg-primary);
}

/* 现代化徽章样式 - 紧凑版 */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-sm);
    letter-spacing: 0.025em;
    border: 1px solid transparent;
    transition: all var(--transition-fast);
    min-width: 50px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left var(--transition-slow);
}

.badge-success {
    background: var(--bg-dark);
    color: var(--text-inverse);
    border: 1px solid var(--color-primary);
}

.badge-warning {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.badge-danger {
    background: var(--bg-dark);
    color: var(--text-inverse);
    border: 1px solid var(--color-primary);
}

.badge-info {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
}

.badge-dark {
    background: var(--bg-dark);
    color: var(--text-inverse);
    border: 1px solid var(--color-primary);
}

/* 现代化分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-xs);
    margin: var(--space-lg) 0;
}

.pagination .page-btn {
    padding: var(--space-sm) var(--space-md);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-base);
    min-width: 32px;
    text-align: center;
}

.pagination .page-btn:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-medium);
    transform: translateY(-1px);
}

.pagination .page-btn.active {
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    color: var(--text-inverse);
    border-color: var(--bg-dark);
    box-shadow: var(--shadow-md);
}

.pagination .page-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
}

.pagination .page-btn:disabled:hover {
    background-color: var(--bg-primary);
    border-color: var(--border-light);
    transform: none;
}

/* 现代化模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
    animation: modalFadeIn var(--transition-base) ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    max-width: 600px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    animation: modalSlideIn var(--transition-base) ease-out;
    /* 隐藏滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.modal-content::-webkit-scrollbar {
    display: none;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    padding: var(--space-2xl) var(--space-2xl) var(--space-xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: -0.02em;
}

.modal-close {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    font-size: var(--text-lg);
    cursor: pointer;
    color: var(--text-primary);
    padding: var(--space-sm);
    border-radius: var(--radius-lg);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--border-medium);
    transform: scale(1.1);
}

.modal-body {
    padding: var(--space-2xl);
}

/* 现代化加载动画 */
.loader {
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    animation: modernSpin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    margin: var(--space-xl) auto;
}

/* 现代化旋转器样式 */
.spinner-border {
    display: inline-block;
    width: 1.2rem;
    height: 1.2rem;
    vertical-align: text-bottom;
    border: 0.15em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: modernSpinner 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.125em;
}

/* 现代化动画关键帧 */
@keyframes modernSpin {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

@keyframes modernSpinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* 内容区域显示 */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* 现代化响应式设计 */
@media (max-width: 1200px) {
    :root {
        --sidebar-width: 260px;
    }

    .content-body {
        padding: var(--space-xl);
    }
}

@media (max-width: 1024px) {
    :root {
        --sidebar-width: 240px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-base);
        box-shadow: var(--shadow-xl);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .content-header {
        padding: var(--space-lg) var(--space-xl);
    }

    .content-header h1 {
        font-size: var(--text-xl);
    }

    .content-body {
        padding: var(--space-xl);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .card-header {
        padding: var(--space-xl);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-md);
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: var(--space-md) var(--space-lg);
    }

    .content-header h1 {
        font-size: var(--text-lg);
    }

    .content-body {
        padding: var(--space-lg);
    }

    .card-header,
    .card-body {
        padding: var(--space-lg);
    }

    .stat-card {
        padding: var(--space-xl);
    }

    .stat-card .stat-value {
        font-size: var(--text-3xl);
    }

    .modal-content {
        width: 95%;
        margin: var(--space-lg);
    }

    .modal-header,
    .modal-body {
        padding: var(--space-xl);
    }
}

/* 现代化侧边栏切换按钮 */
.sidebar-toggle {
    position: fixed;
    top: var(--space-xl);
    left: var(--space-xl);
    z-index: 1001;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    color: var(--text-inverse);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    cursor: pointer;
    display: none;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-base);
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

@media (max-width: 768px) {
    .sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 工具提示 */
/* 移除工具提示 */

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--space-3xl);
    color: var(--color-dark-gray);
}

.empty-state svg {
    width: 64px;
    height: 64px;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-sm);
    color: var(--color-primary);
}

.empty-state p {
    font-size: var(--text-sm);
    margin-bottom: var(--space-lg);
}

/* 成功/错误消息 */
.alert {
    padding: var(--space-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-lg);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--color-success);
    color: var(--color-success);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--color-warning);
    color: var(--color-warning);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: var(--color-danger);
    color: var(--color-danger);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: var(--color-info);
    color: var(--color-info);
} 

/* 批量操作工具栏 - 优化 */
.batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
    padding: var(--space-sm) var(--space-md);
    background-color: var(--color-light-gray);
    border-radius: var(--radius-sm);
    border: 1px solid var(--color-medium-gray);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.batch-info {
    margin-left: var(--space-md);
    color: var(--color-dark-gray);
    font-size: var(--text-xs);
}

/* 表格操作按钮 */
.table .actions {
    white-space: nowrap;
}

.table .actions .btn {
    margin-right: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    border-radius: var(--radius-sm);
}

.table .actions .btn:last-child {
    margin-right: 0;
}

/* 邀请码显示 */
.code-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    letter-spacing: 1px;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* 复选框样式 */
.code-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.code-checkbox:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 现代化提示消息动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
    to {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
    }
}

/* 现代化Toast样式 */
.toast {
    position: fixed;
    top: var(--space-2xl);
    right: var(--space-2xl);
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-xl);
    color: var(--text-inverse);
    font-weight: 600;
    z-index: 10000;
    box-shadow: var(--shadow-xl);
    max-width: 400px;
    word-wrap: break-word;
    backdrop-filter: blur(10px);
    animation: slideInRight var(--transition-base) ease-out;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.toast-success {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.toast-error {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-tertiary) 100%);
}

.toast-warning {
    background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-quaternary) 100%);
    color: var(--text-inverse);
}

.toast-info {
    background: linear-gradient(135deg, var(--color-info) 0%, var(--color-accent) 100%);
}

/* 卡片头部动作按钮 */
.card-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 按钮尺寸优化 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* 表格响应式优化 */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
    }
    
    .batch-toolbar {
        flex-direction: column;
        gap: 10px;
    }
    
    .batch-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .batch-info {
        margin-left: 0;
        text-align: center;
    }
}

/* 禁用状态的按钮样式 - 黑白风格 */
.btn:disabled {
    background-color: var(--color-light-gray);
    color: var(--color-dark-gray);
    border: 1px solid var(--color-dark-gray);
    cursor: not-allowed;
}

/* 加载状态优化 */
.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态徽章优化 */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: var(--color-white);
    background-color: var(--color-success);
}

.badge-warning {
    color: var(--color-black);
    background-color: var(--color-warning);
}

.badge-danger {
    color: var(--color-white);
    background-color: var(--color-danger);
} 

/* 批量生成邀请码样式 */
.form-row {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.form-row .form-group {
    flex: 1;
}

.form-text {
    font-size: var(--text-sm);
    color: var(--color-dark-gray);
    margin-top: var(--space-xs);
}

.batch-result {
    margin-top: var(--space-lg);
    padding: var(--space-lg);
    background-color: var(--color-white);
    border-radius: 0;
    border: 1px solid var(--color-black);
}

.batch-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 1px solid var(--color-black);
}

.batch-result-header h4 {
    margin: 0;
    color: var(--color-black);
    font-size: var(--text-lg);
    font-weight: 600;
}

.batch-result-actions {
    display: flex;
    gap: var(--space-sm);
}

.batch-result-summary {
    margin-bottom: var(--space-md);
    padding: var(--space-sm);
    background-color: var(--color-black);
    color: var(--color-white);
    border-radius: 0;
    font-weight: 600;
}

.batch-codes-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--color-black);
    border-radius: 0;
    background-color: var(--color-white);
}

.batch-codes-list {
    padding: var(--space-sm);
}

.batch-code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm);
    margin-bottom: var(--space-xs);
    background-color: var(--color-white);
    border-radius: 0;
    border: 1px solid var(--color-black);
}

.batch-code-item:last-child {
    margin-bottom: 0;
}

.batch-code-text {
    font-family: var(--font-mono);
    font-weight: 600;
    letter-spacing: 1px;
    color: var(--color-black);
}

.batch-code-item .btn {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
}

/* 加载状态样式 */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125em;
}

@keyframes spinner-border {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: var(--space-sm);
    }
    
    .batch-result-header {
        flex-direction: column;
        gap: var(--space-sm);
        align-items: flex-start;
    }
    
    .batch-result-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .batch-code-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-xs);
    }
    
    .batch-code-item .btn {
        align-self: flex-end;
    }
}

/* 按钮outline样式 */
.btn-outline-primary {
    color: var(--color-primary);
    border-color: var(--color-primary);
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.btn-outline-primary:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.25);
}

/* 现代化工具类 */
.text-muted {
    color: var(--text-muted);
}

/* 现代化特效类 */
.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
    transition: transform var(--transition-base);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.fade-in {
    animation: fadeIn var(--transition-slow) ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 现代化焦点样式 */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* 现代化禁用状态 */
.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 现代化文本选择 */
::selection {
    background: var(--color-primary);
    color: var(--text-inverse);
}

/* 现代化滚动行为 */
html {
    scroll-behavior: smooth;
}

/* 现代化代码显示 */
.code-display {
    font-family: var(--font-mono);
    font-weight: 600;
    letter-spacing: 0.05em;
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    font-size: var(--text-sm);
}

/* 现代化复选框样式 */
.code-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--color-primary);
    border-radius: var(--radius-sm);
}

.code-checkbox:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 现代化增强样式 */

/* 卡片头部操作按钮组 */
.card-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-wrap: wrap;
}

/* 现代化空状态 */
.empty-state {
    text-align: center;
    padding: var(--space-4xl);
    color: var(--text-tertiary);
}

.empty-state svg {
    width: 80px;
    height: 80px;
    margin-bottom: var(--space-xl);
    opacity: 0.3;
    stroke: var(--text-tertiary);
}

.empty-state h3 {
    font-size: var(--text-xl);
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
    font-weight: 600;
}

.empty-state p {
    font-size: var(--text-base);
    margin-bottom: var(--space-xl);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* 现代化警告框 */
.alert {
    padding: var(--space-xl);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-xl);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    backdrop-filter: blur(10px);
}

.alert-success {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(64, 64, 64, 0.05) 0%, rgba(64, 64, 64, 0.02) 100%);
    border-color: var(--color-accent);
    color: var(--text-primary);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.04) 100%);
    border-color: var(--color-primary);
    color: var(--text-primary);
}

.alert-info {
    background: linear-gradient(135deg, rgba(102, 102, 102, 0.05) 0%, rgba(102, 102, 102, 0.02) 100%);
    border-color: var(--color-info);
    color: var(--text-primary);
}

/* 现代化批量操作工具栏 */
.batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    padding: var(--space-lg) var(--space-xl);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.batch-info {
    color: var(--text-tertiary);
    font-size: var(--text-sm);
    font-weight: 500;
}

/* 现代化表格操作按钮 */
.table .actions {
    white-space: nowrap;
}

.table .actions .btn {
    margin-right: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
}

.table .actions .btn:last-child {
    margin-right: 0;
}

/* 现代化按钮禁用状态 */
.btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    border: 1px solid var(--border-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn:disabled::before {
    display: none;
}

/* 现代化表单行 */
.form-row {
    display: flex;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-text {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    margin-top: var(--space-sm);
    line-height: 1.4;
}

/* 现代化批量结果展示 */
.batch-result {
    margin-top: var(--space-2xl);
    padding: var(--space-2xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-md);
}

.batch-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
}

.batch-result-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--text-xl);
    font-weight: 700;
}

.batch-result-actions {
    display: flex;
    gap: var(--space-md);
}

.batch-result-summary {
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    color: var(--text-inverse);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-align: center;
}

.batch-codes-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    /* 隐藏滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.batch-codes-container::-webkit-scrollbar {
    display: none;
}

.batch-codes-list {
    padding: var(--space-lg);
}

.batch-code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
    margin-bottom: var(--space-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.batch-code-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
}

.batch-code-item:last-child {
    margin-bottom: 0;
}

.batch-code-text {
    font-family: var(--font-mono);
    font-weight: 700;
    letter-spacing: 0.1em;
    color: var(--text-primary);
    font-size: var(--text-base);
}

.batch-code-item .btn {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
}

/* 邀请码查看列表样式 */
.codes-view-list {
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
}

.code-view-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-light);
    transition: background-color var(--transition-fast);
}

.code-view-item:last-child {
    border-bottom: none;
}

.code-view-item:hover {
    background-color: var(--bg-tertiary);
}

.code-view-info {
    flex: 1;
}

.code-view-code {
    font-family: var(--font-mono);
    font-weight: 600;
    font-size: var(--text-sm);
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.code-view-meta {
    display: flex;
    gap: var(--space-md);
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.code-view-status {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 600;
}

.code-view-status.unused {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.code-view-status.used {
    background-color: var(--color-primary);
    color: var(--text-inverse);
}

.code-view-status.expired {
    background-color: var(--color-accent);
    color: var(--text-inverse);
}

.loading-text {
    text-align: center;
    padding: var(--space-xl);
    color: var(--text-secondary);
    font-style: italic;
}

.empty-text {
    text-align: center;
    padding: var(--space-xl);
    color: var(--text-muted);
}

/* API文档样式 */
.api-section {
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-xl);
    border-bottom: 1px solid var(--border-light);
}

.api-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.api-section-title {
    display: flex;
    align-items: center;
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.api-section-icon {
    font-size: var(--text-xl);
    margin-right: var(--space-sm);
}

.api-description {
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
    line-height: 1.6;
}

.api-endpoint {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--color-primary);
}

.method {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-weight: 700;
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.method.get {
    background: #10b981;
    color: white;
}

.method.post {
    background: #3b82f6;
    color: white;
}

.method.put {
    background: #f59e0b;
    color: white;
}

.method.delete {
    background: #ef4444;
    color: white;
}

.url {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--text-primary);
    background: var(--bg-secondary);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
}

.code-block {
    position: relative;
    margin-bottom: var(--space-lg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--bg-secondary);
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
}

.code-title {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.copy-btn {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    background: transparent;
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-size: var(--text-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.copy-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-dark);
}

.copy-btn svg {
    width: 14px;
    height: 14px;
}

.code-block pre {
    margin: 0;
    padding: var(--space-md);
    background: transparent;
    border: none;
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    line-height: 1.5;
    color: var(--text-primary);
    overflow-x: auto;
}

.code-block code {
    background: transparent;
    padding: 0;
    border-radius: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
}

.response-example {
    margin-top: var(--space-lg);
}

.response-example h4 {
    font-size: var(--text-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.api-params {
    margin-bottom: var(--space-lg);
}

.api-params h4 {
    font-size: var(--text-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.params-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--space-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    overflow: hidden;
    border: 1px solid var(--border-light);
}

.params-table th,
.params-table td {
    padding: var(--space-md);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.params-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--text-sm);
}

.params-table td {
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.params-table tr:last-child td {
    border-bottom: none;
}

.params-table code {
    background: var(--bg-tertiary);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-family: var(--font-mono);
    font-size: var(--text-xs);
    color: var(--text-primary);
}

.error-codes {
    margin-bottom: var(--space-lg);
}

.error-codes h4 {
    font-size: var(--text-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

/* 页面切换动画 */
.content-section {
    animation: fadeInUp 0.6s var(--ease-out-cubic) forwards;
    opacity: 0;
    transform: translateY(20px);
}

.content-section.active {
    opacity: 1;
    transform: translateY(0);
}

/* 卡片进入动画 */
.card {
    animation: cardFadeIn 0.8s var(--ease-out-cubic) forwards;
    animation-delay: 0.1s;
    opacity: 0;
    transform: translateY(30px);
}

.card:nth-child(2) {
    animation-delay: 0.2s;
}

.card:nth-child(3) {
    animation-delay: 0.3s;
}

.card:nth-child(4) {
    animation-delay: 0.4s;
}

/* 关键帧动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes cardFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表格行动画 */
.table tbody tr {
    animation: tableRowFadeIn 0.5s var(--ease-out-cubic) forwards;
    opacity: 0;
    transform: translateX(-10px);
}

.table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.table tbody tr:nth-child(2) { animation-delay: 0.15s; }
.table tbody tr:nth-child(3) { animation-delay: 0.2s; }
.table tbody tr:nth-child(4) { animation-delay: 0.25s; }
.table tbody tr:nth-child(5) { animation-delay: 0.3s; }

@keyframes tableRowFadeIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 优化滚动行为 */
html {
    scroll-behavior: smooth;
}

/* 焦点状态优化 */
*:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* 选择文本样式 */
::selection {
    background-color: var(--color-primary);
    color: var(--text-inverse);
}