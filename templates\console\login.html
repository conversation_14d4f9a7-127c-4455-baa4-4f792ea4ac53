<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 邀请码核销系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-console.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: var(--space-lg);
        }
        
        .login-container {
            background-color: var(--color-white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 420px;
            padding: var(--space-3xl);
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: var(--space-3xl);
        }
        
        .login-logo h2 {
            font-size: var(--text-3xl);
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: var(--space-sm);
            letter-spacing: -0.025em;
        }
        
        .login-logo p {
            font-size: var(--text-base);
            color: var(--color-dark-gray);
            font-weight: 500;
        }
        
        .login-form .form-group {
            margin-bottom: var(--space-xl);
        }
        
        .login-form label {
            display: block;
            margin-bottom: var(--space-sm);
            font-weight: 600;
            color: var(--color-primary);
            font-size: var(--text-sm);
        }
        
        .login-form input {
            width: 100%;
            padding: var(--space-lg);
            border: 2px solid var(--color-medium-gray);
            border-radius: var(--radius-lg);
            font-size: var(--text-base);
            transition: all var(--transition-base);
            background-color: var(--color-white);
            font-family: inherit;
        }
        
        .login-form input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .login-form input:hover {
            border-color: var(--color-dark-gray);
        }
        
        .login-form button {
            width: 100%;
            padding: var(--space-lg);
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            color: var(--color-white);
            border: none;
            border-radius: var(--radius-lg);
            font-size: var(--text-base);
            font-weight: 700;
            cursor: pointer;
            transition: all var(--transition-base);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            overflow: hidden;
        }
        
        .login-form button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left var(--transition-base);
        }
        
        .login-form button:hover::before {
            left: 100%;
        }
        
        .login-form button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .login-form button:active {
            transform: translateY(0);
        }
        
        .login-form button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .login-links {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--space-xl);
            font-size: var(--text-sm);
        }
        
        .login-links a {
            color: var(--color-dark-gray);
            text-decoration: none;
            font-weight: 500;
            transition: color var(--transition-base);
        }
        
        .login-links a:hover {
            color: var(--color-primary);
        }
        
        .error-message {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--color-danger);
            padding: var(--space-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-xl);
            display: none;
            border: 1px solid var(--color-danger);
            font-weight: 500;
        }
        
        .success-message {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--color-success);
            padding: var(--space-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-xl);
            display: none;
            border: 1px solid var(--color-success);
            font-weight: 500;
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid var(--color-white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: var(--space-sm);
        }
        
        .login-form button.loading {
            pointer-events: none;
        }
        
        .login-form button.loading .loading-spinner {
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 动画效果 */
        .login-container {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                padding: var(--space-xl);
                margin: var(--space-lg);
            }
            
            .login-logo h2 {
                font-size: var(--text-2xl);
            }
            
            .login-links {
                flex-direction: column;
                gap: var(--space-md);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <h2>邀请码核销系统</h2>
            <p>管理控制台登录</p>
        </div>
        
        <div class="error-message" id="error-message"></div>
        <div class="success-message" id="success-message"></div>
        
        <form class="login-form" id="login-form">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <button type="submit" id="login-btn">
                <span class="loading-spinner"></span>
                <span class="btn-text">登录</span>
            </button>
        </form>
        
        <div class="login-links">
            <a href="{{ url_for('index') }}">返回首页</a>
            <a href="#" id="register-link">注册账号</a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');
            const registerLink = document.getElementById('register-link');
            const loginBtn = document.getElementById('login-btn');
            const btnText = loginBtn.querySelector('.btn-text');
            let isRegistering = false;
            
            // 检查是否已登录
            checkLoginStatus();
            
            // 登录表单提交
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (isRegistering) {
                    handleRegister();
                } else {
                    handleLogin();
                }
            });
            
            // 处理登录
            function handleLogin() {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                // 验证输入
                if (!username || !password) {
                    showError('用户名和密码不能为空');
                    return;
                }
                
                // 显示加载状态
                setLoading(true);
                
                // 发送登录请求
                fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ username, password }),
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (!response.ok) {
                        if (response.status === 401) {
                            throw new Error('用户名或密码错误');
                        } else if (response.status === 429) {
                            throw new Error('请求过于频繁，请稍后再试');
                        } else {
                            throw new Error('登录失败，请稍后再试');
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 显示成功消息
                        showSuccess('登录成功，正在跳转...');
                        
                        // 保存用户信息
                        sessionStorage.setItem('user', JSON.stringify(data.data.user));
                        
                        // 延迟跳转以显示成功消息
                        setTimeout(() => {
                            window.location.href = '/dashboard';
                        }, 1000);
                    } else {
                        showError(data.error || '登录失败');
                    }
                })
                .catch(error => {
                    showError(error.message || '网络错误，请稍后重试');
                    console.error('登录错误:', error);
                })
                .finally(() => {
                    setLoading(false);
                });
            }
            
            // 处理注册
            function handleRegister() {
                const username = document.getElementById('reg-username').value;
                const email = document.getElementById('reg-email').value;
                const password = document.getElementById('reg-password').value;
                const confirmPassword = document.getElementById('reg-confirm-password').value;
                
                // 验证输入
                if (!username || !email || !password) {
                    showError('所有字段都是必填的');
                    return;
                }
                
                if (password !== confirmPassword) {
                    showError('两次输入的密码不一致');
                    return;
                }
                
                if (password.length < 8) {
                    showError('密码长度至少为8个字符');
                    return;
                }
                
                // 显示加载状态
                setLoading(true);
                
                // 发送注册请求
                fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ username, email, password }),
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (!response.ok) {
                        if (response.status === 400) {
                            return response.json().then(data => {
                                throw new Error(data.error || '注册数据无效');
                            });
                        } else if (response.status === 429) {
                            throw new Error('请求过于频繁，请稍后再试');
                        } else {
                            throw new Error('注册失败，请稍后再试');
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showSuccess('注册成功！请登录');
                        // 切换回登录模式
                        setTimeout(() => {
                            toggleMode();
                        }, 1500);
                    } else {
                        showError(data.error || '注册失败');
                    }
                })
                .catch(error => {
                    showError(error.message || '网络错误，请稍后重试');
                    console.error('注册错误:', error);
                })
                .finally(() => {
                    setLoading(false);
                });
            }
            
            // 显示错误消息
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                successMessage.style.display = 'none';
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
            }
            
            // 显示成功消息
            function showSuccess(message) {
                successMessage.textContent = message;
                successMessage.style.display = 'block';
                errorMessage.style.display = 'none';
            }
            
            // 设置加载状态
            function setLoading(loading) {
                if (loading) {
                    loginBtn.classList.add('loading');
                    btnText.textContent = '处理中...';
                } else {
                    loginBtn.classList.remove('loading');
                    btnText.textContent = isRegistering ? '注册' : '登录';
                }
            }
            
            // 检查登录状态
            function checkLoginStatus() {
                // 检查sessionStorage中是否有用户数据
                const userDataStr = sessionStorage.getItem('user');
                if (userDataStr) {
                    try {
                        const userData = JSON.parse(userDataStr);
                        if (userData.username) {
                            // 向服务器验证JWT是否仍然有效
                            fetch('/api/auth/profile', {
                                method: 'GET',
                                headers: {
                                    'X-Requested-With': 'XMLHttpRequest'
                                },
                                credentials: 'same-origin'
                            })
                            .then(response => {
                                if (response.ok) {
                                    return response.json();
                                }
                                throw new Error('JWT已过期');
                            })
                            .then(data => {
                                if (data.success) {
                                    // JWT有效，跳转到控制台
                                    window.location.href = '/dashboard';
                                } else {
                                    // 清除无效的用户数据
                                    sessionStorage.removeItem('user');
                                }
                            })
                            .catch(error => {
                                // JWT无效或其他错误，清除本地数据
                                sessionStorage.removeItem('user');
                                console.log('用户会话已过期，已清除本地数据');
                            });
                            return;
                        }
                    } catch (e) {
                        // 清除无效的用户数据
                        sessionStorage.removeItem('user');
                    }
                }
            }
            
            // 注册链接点击事件
            registerLink.addEventListener('click', function(e) {
                e.preventDefault();
                toggleMode();
            });
            
            // 切换登录/注册模式
            function toggleMode() {
                isRegistering = !isRegistering;
                
                if (isRegistering) {
                    // 切换到注册模式
                    document.querySelector('.login-logo h2').textContent = '用户注册';
                    document.querySelector('.login-logo p').textContent = '创建新账户';
                    btnText.textContent = '注册';
                    registerLink.textContent = '已有账户？登录';
                    
                    // 添加额外的注册字段
                    const emailGroup = document.createElement('div');
                    emailGroup.className = 'form-group';
                    emailGroup.innerHTML = `
                        <label for="reg-email">邮箱</label>
                        <input type="email" id="reg-email" name="email" required autocomplete="email">
                    `;
                    
                    const confirmGroup = document.createElement('div');
                    confirmGroup.className = 'form-group';
                    confirmGroup.innerHTML = `
                        <label for="reg-confirm-password">确认密码</label>
                        <input type="password" id="reg-confirm-password" name="confirm_password" required>
                    `;
                    
                    // 更改现有字段的ID
                    document.getElementById('username').id = 'reg-username';
                    document.getElementById('password').id = 'reg-password';
                    
                    // 插入新字段
                    const passwordGroup = document.querySelector('.form-group:last-of-type');
                    passwordGroup.parentNode.insertBefore(emailGroup, passwordGroup);
                    passwordGroup.parentNode.insertBefore(confirmGroup, passwordGroup.nextSibling);
                    
                } else {
                    // 切换到登录模式
                    document.querySelector('.login-logo h2').textContent = '邀请码核销系统';
                    document.querySelector('.login-logo p').textContent = '管理控制台登录';
                    btnText.textContent = '登录';
                    registerLink.textContent = '注册账号';
                    
                    // 移除注册字段
                    const emailGroup = document.querySelector('#reg-email')?.parentNode;
                    const confirmGroup = document.querySelector('#reg-confirm-password')?.parentNode;
                    
                    if (emailGroup) emailGroup.remove();
                    if (confirmGroup) confirmGroup.remove();
                    
                    // 恢复原始字段ID
                    const usernameField = document.getElementById('reg-username');
                    const passwordField = document.getElementById('reg-password');
                    
                    if (usernameField) usernameField.id = 'username';
                    if (passwordField) passwordField.id = 'password';
                }
                
                // 清除消息
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';
                
                // 重置表单
                loginForm.reset();
            }
        });
    </script>
</body>
</html>