from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity, set_access_cookies, unset_jwt_cookies
from werkzeug.security import generate_password_hash, check_password_hash
import re
import functools
import logging

from models import db, User
from utils.helpers import api_response, bad_request, unauthorized, not_found, forbidden
from services.admin_service import validate_admin_password, change_admin_password

# 配置日志
logger = logging.getLogger(__name__)

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)

# 验证邮箱格式的正则表达式
EMAIL_REGEX = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')

def admin_required(f):
    """管理员权限装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))
        if not user or (not user.is_admin and not user.has_role('admin')):
            return forbidden("需要管理员权限")
        return f(*args, **kwargs)
    return decorated_function

def role_required(role_name):
    """角色权限装饰器"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = get_jwt_identity()
            user = User.query.get(int(user_id))
            if not user or not user.has_role(role_name):
                return forbidden(f"需要 {role_name} 角色权限")
            return f(*args, **kwargs)
        return decorated_function
    return decorator

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()
    
    # 验证请求数据
    if not data:
        return bad_request("缺少请求数据")
    
    username = data.get('username')
    email = data.get('email')
    password = data.get('password')
    is_admin = data.get('is_admin', False)
    
    if not username or not email or not password:
        return bad_request("用户名、邮箱和密码为必填项")
    
    # 验证用户名格式
    if not 3 <= len(username) <= 20:
        return bad_request("用户名长度必须在3-20个字符之间")
    
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        return bad_request("用户名只能包含字母、数字和下划线")
    
    # 验证邮箱格式
    if not EMAIL_REGEX.match(email):
        return bad_request("邮箱格式不正确")
    
    # 验证密码强度
    if len(password) < 8:
        return bad_request("密码长度至少为8个字符")
    
    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=username).first():
        return bad_request("用户名已存在")
    
    if User.query.filter_by(email=email).first():
        return bad_request("邮箱已存在")
    
    # 创建新用户
    user = User(
        username=username,
        email=email
    )
    user.password = password
    
    # 第一个用户设为管理员
    if User.query.count() == 0:
        user.is_admin = True
        user.add_role('admin')
    # 如果请求中指定了管理员权限且当前用户是管理员，则设置管理员权限
    elif is_admin:
        # 验证当前用户是否是管理员
        try:
            current_user_id = get_jwt_identity()
            if current_user_id:  # 如果已登录
                current_user = User.query.get(int(current_user_id))
                if current_user and (current_user.is_admin or current_user.has_role('admin')):
                    user.is_admin = True
                    user.add_role(current_app.config.get('DEFAULT_ADMIN_ROLE', 'admin'))
        except:
            # 如果获取当前用户失败，不设置管理员权限
            pass
    else:
        # 普通用户添加默认用户角色
        user.add_role(current_app.config.get('DEFAULT_USER_ROLE', 'user'))
    
    try:
        db.session.add(user)
        db.session.commit()
        
        # 生成访问令牌
        access_token = create_access_token(identity=str(user.id))
        
        # 创建响应
        response = api_response(
            data={
                'user': user.to_dict()
            },
            message="注册成功",
            status_code=201
        )
        
        # 设置 JWT 令牌到 HttpOnly Cookie
        set_access_cookies(response[0], access_token)
        
        return response
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"用户注册失败: {str(e)}")
        return bad_request("注册失败，请稍后重试")

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    
    # 验证请求数据
    if not data:
        return bad_request("缺少请求数据")
    
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return bad_request("用户名和密码为必填项")
    
    # 查找用户
    user = User.query.filter_by(username=username).first()
    
    # 检查账户是否被锁定
    if user and user.is_locked():
        return unauthorized("账户已被锁定，请稍后再试")
    
    if not user or not user.verify_password(password):
        # 记录登录失败
        if user:
            user.increment_login_attempts()
        return unauthorized("用户名或密码错误")
    
    try:
        # 重置登录尝试次数
        user.reset_login_attempts()
        
        # 生成访问令牌
        access_token = create_access_token(identity=str(user.id))
        
        # 创建响应
        response = api_response(
            data={
                'user': user.to_dict()
            },
            message="登录成功"
        )
        
        # 设置 JWT 令牌到 HttpOnly Cookie
        set_access_cookies(response[0], access_token)
        
        return response
    except Exception as e:
        current_app.logger.error(f"用户登录失败: {str(e)}")
        return bad_request("登录失败，请稍后重试")

@auth_bp.route('/change_password', methods=['POST'])
@jwt_required()
def update_password():
    """修改密码"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))
        
        if not user:
            return not_found("用户不存在")
        
        data = request.get_json()
        
        if not data:
            return bad_request("缺少请求数据")
        
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        
        if not current_password or not new_password:
            return bad_request("当前密码和新密码为必填项")
        
        # 验证当前密码
        if not user.verify_password(current_password):
            return unauthorized("当前密码错误")
        
        # 检查是否为管理员账户
        if user.is_admin or user.has_role('admin') or user.has_role('super_admin'):
            # 验证密码强度
            is_valid, error_msg = validate_admin_password(new_password)
            if not is_valid:
                return bad_request(error_msg)
            
            # 检查密码历史
            if user.check_password_history(new_password):
                return bad_request("新密码不能与最近使用过的密码相同")
        else:
            # 普通用户密码验证
            if len(new_password) < 8:
                return bad_request("密码长度至少为8个字符")
        
        # 更新密码
        user.password = new_password
        db.session.commit()
        
        return api_response(
            message="密码修改成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修改密码失败: {str(e)}")
        return bad_request("修改密码失败，请稍后重试")

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户注销"""
    try:
        # 创建响应
        response = api_response(
            message="注销成功"
        )
        
        # 清除 JWT Cookie
        unset_jwt_cookies(response[0])
        
        return response
    except Exception as e:
        current_app.logger.error(f"用户注销失败: {str(e)}")
        return bad_request("注销失败，请稍后重试")

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取当前用户信息"""
    try:
        user_id = get_jwt_identity()
        # 将字符串ID转换为整数
        user = User.query.get(int(user_id))
        
        if not user:
            return not_found("用户不存在")
        
        return api_response(
            data=user.to_dict(),
            message="获取用户信息成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取用户信息失败: {str(e)}")
        return bad_request("获取用户信息失败，请稍后重试")

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户信息"""
    try:
        user_id = get_jwt_identity()
        # 将字符串ID转换为整数
        user = User.query.get(int(user_id))
        
        if not user:
            return not_found("用户不存在")
        
        data = request.get_json()
        
        if not data:
            return bad_request("缺少请求数据")
        
        # 更新邮箱
        if 'email' in data:
            email = data['email']
            
            # 验证邮箱格式
            if not EMAIL_REGEX.match(email):
                return bad_request("邮箱格式不正确")
                
            if email != user.email and User.query.filter_by(email=email).first():
                return bad_request("邮箱已存在")
            user.email = email
        
        # 更新密码
        if 'password' in data:
            password = data['password']
            
            # 验证密码强度
            if len(password) < 8:
                return bad_request("密码长度至少为8个字符")
                
            user.password = password
        
        db.session.commit()
        
        return api_response(
            data=user.to_dict(),
            message="更新用户信息成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户信息失败: {str(e)}")
        return bad_request("更新用户信息失败，请稍后重试")

# 用户管理相关接口（仅管理员可访问）

@auth_bp.route('/users', methods=['GET'])
@jwt_required()
@admin_required
def get_users():
    """获取用户列表（仅管理员）"""
    try:
        # 查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # 限制每页最多100条
        
        # 使用优化的预加载查询
        pagination = User.get_users_with_roles(page=page, per_page=per_page)
        
        users = pagination.items
        
        return api_response(
            data={
                'users': [user.to_dict() for user in users],
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page
            },
            message="获取用户列表成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取用户列表失败: {str(e)}")
        return bad_request("获取用户列表失败，请稍后重试")

@auth_bp.route('/users/<int:user_id>', methods=['GET'])
@jwt_required()
@admin_required
def get_user(user_id):
    """获取单个用户信息（仅管理员）"""
    try:
        user = User.query.get(user_id)
        
        if not user:
            return not_found("用户不存在")
        
        return api_response(
            data=user.to_dict(),
            message="获取用户信息成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取用户信息失败: {str(e)}")
        return bad_request("获取用户信息失败，请稍后重试")

@auth_bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_user(user_id):
    """更新用户信息（仅管理员）"""
    try:
        user = User.query.get(user_id)
        
        if not user:
            return not_found("用户不存在")
        
        data = request.get_json()
        
        if not data:
            return bad_request("缺少请求数据")
        
        # 更新邮箱
        if 'email' in data:
            email = data['email']
            
            # 验证邮箱格式
            if not EMAIL_REGEX.match(email):
                return bad_request("邮箱格式不正确")
                
            if email != user.email and User.query.filter_by(email=email).first():
                return bad_request("邮箱已存在")
            user.email = email
        
        # 更新管理员状态
        if 'is_admin' in data:
            # 防止最后一个管理员被取消管理员权限
            if user.is_admin and not data['is_admin']:
                admin_count = User.query.filter_by(is_admin=True).count()
                if admin_count <= 1:
                    return bad_request("至少需要保留一个管理员账号")
            
            user.is_admin = bool(data['is_admin'])
        
        # 重置密码
        if 'password' in data:
            password = data['password']
            
            # 验证密码强度
            if len(password) < 8:
                return bad_request("密码长度至少为8个字符")
                
            user.password = password
        
        db.session.commit()
        
        return api_response(
            data=user.to_dict(),
            message="更新用户信息成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户信息失败: {str(e)}")
        return bad_request("更新用户信息失败，请稍后重试")

@auth_bp.route('/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_user(user_id):
    """删除用户（仅管理员）"""
    try:
        current_user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        
        if not user:
            return not_found("用户不存在")
        
        # 防止删除自己
        if user.id == current_user_id:
            return bad_request("不能删除自己的账号")
        
        # 防止删除最后一个管理员
        if user.is_admin:
            admin_count = User.query.filter_by(is_admin=True).count()
            if admin_count <= 1:
                return bad_request("不能删除最后一个管理员账号")
        
        db.session.delete(user)
        db.session.commit()
        
        return api_response(
            message="删除用户成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除用户失败: {str(e)}")
        return bad_request("删除用户失败，请稍后重试") 

@auth_bp.route('/users', methods=['POST'])
@jwt_required()
@admin_required
def create_user():
    """创建用户（仅管理员）"""
    try:
        data = request.get_json()
        
        if not data:
            return bad_request("缺少请求数据")
        
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        is_admin = data.get('is_admin', False)
        
        if not username or not email or not password:
            return bad_request("用户名、邮箱和密码为必填项")
        
        # 验证用户名格式
        if not 3 <= len(username) <= 20:
            return bad_request("用户名长度必须在3-20个字符之间")
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return bad_request("用户名只能包含字母、数字和下划线")
        
        # 验证邮箱格式
        if not EMAIL_REGEX.match(email):
            return bad_request("邮箱格式不正确")
        
        # 验证密码强度
        if len(password) < 8:
            return bad_request("密码长度至少为8个字符")
        
        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            return bad_request("用户名已存在")
        
        if User.query.filter_by(email=email).first():
            return bad_request("邮箱已存在")
        
        # 创建新用户
        user = User(
            username=username,
            email=email,
            is_admin=is_admin
        )
        user.password = password
        
        db.session.add(user)
        db.session.commit()
        
        return api_response(
            data=user.to_dict(),
            message="用户创建成功",
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建用户失败: {str(e)}")
        return bad_request("创建用户失败，请稍后重试") 

@auth_bp.route('/password-requirements', methods=['GET'])
def get_password_requirements():
    """获取密码要求配置"""
    try:
        requirements = {
            'min_length': current_app.config.get('ADMIN_PASSWORD_MIN_LENGTH', 10),
            'require_uppercase': current_app.config.get('ADMIN_PASSWORD_REQUIRE_UPPERCASE', True),
            'require_numbers': current_app.config.get('ADMIN_PASSWORD_REQUIRE_NUMBERS', True),
            'require_special': current_app.config.get('ADMIN_PASSWORD_REQUIRE_SPECIAL', True)
        }
        
        return api_response(
            data=requirements,
            message="获取密码要求成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取密码要求失败: {str(e)}")
        return bad_request("获取密码要求失败，请稍后重试") 