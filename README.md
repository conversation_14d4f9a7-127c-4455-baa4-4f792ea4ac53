# 邀请码核销系统

一个简约高效的邀请码管理平台，提供邀请码生成、核销和管理功能。

## 功能特点

- **邀请码管理**：生成、查询、核销邀请码
- **用户认证**：JWT身份验证，保护API安全
- **高并发支持**：使用锁机制防止重复核销
- **简约黑白风格**：美观易用的用户界面
- **响应式设计**：适配各种设备屏幕

## 技术栈

- **后端**：Python + Flask
- **数据库**：SQLite
- **前端**：HTML5 + CSS3 + JavaScript
- **认证**：JWT (JSON Web Token)

## 项目结构

```
invite-verify/
├── app.py             # Flask应用入口
├── config.py          # 配置文件
├── models.py          # 数据库模型
├── routes/            # API路由
│   ├── __init__.py
│   ├── auth.py        # 认证相关路由
│   └── invite.py      # 邀请码相关路由
├── services/          # 业务逻辑
│   ├── __init__.py
│   └── invite.py      # 邀请码服务
├── static/            # 静态资源
│   ├── css/
│   ├── js/
│   └── img/
├── templates/         # HTML模板
│   ├── index.html     # 前端门户
│   └── console/       # 控制台页面
│       ├── login.html
│       └── dashboard.html
├── utils/             # 工具函数
│   ├── __init__.py
│   └── helpers.py
└── requirements.txt   # 依赖项
```

## 安装与运行

### 环境要求

- Python 3.8+
- pip

### 安装步骤

1. 克隆项目到本地

```bash
git clone https://github.com/yourusername/invite-verify.git
cd invite-verify
```

2. 安装依赖

```bash
pip install -r requirements.txt
```

3. 运行应用

```bash
python app.py
```

4. 访问应用

打开浏览器，访问 `http://localhost:5000`

## API 接口说明

### 认证相关

- `POST /api/auth/register`：用户注册
- `POST /api/auth/login`：用户登录
- `GET /api/auth/profile`：获取用户信息
- `PUT /api/auth/profile`：更新用户信息

### 邀请码相关

- `POST /api/invite/generate`：生成邀请码
- `POST /api/invite/redeem`：核销邀请码
- `GET /api/invite/codes`：获取邀请码列表
- `GET /api/invite/codes/<code>`：获取单个邀请码详情

## 性能优化

1. **数据库索引**：为邀请码字段创建索引，提高查询效率
2. **请求频率限制**：防止恶意请求
3. **线程锁**：防止邀请码重复核销
4. **批量生成功能**：支持批量生成邀请码，提高效率

## 安全措施

1. **JWT认证**：保护API接口安全
2. **密码加密**：使用哈希算法存储密码
3. **请求频率限制**：防止暴力攻击
4. **输入验证**：防止注入攻击

## 未来计划

- [ ] 添加Redis缓存支持
- [ ] 支持邀请码导出功能
- [ ] 添加更详细的统计分析
- [ ] 支持多语言

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

MIT License 