<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无滚动条测试 - 邀请码核销系统</title>
    <link rel="stylesheet" href="static/css/modern-console.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>邀请码核销系统</h2>
                <p>无滚动条测试</p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="#dashboard" class="nav-link active">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="7" height="7"></rect>
                                <rect x="14" y="3" width="7" height="7"></rect>
                                <rect x="14" y="14" width="7" height="7"></rect>
                                <rect x="3" y="14" width="7" height="7"></rect>
                            </svg>
                            仪表盘
                        </a>
                    </li>
                    <li>
                        <a href="#invite-codes" class="nav-link">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                            邀请码管理
                        </a>
                    </li>
                    <!-- 添加更多导航项来测试侧边栏滚动 -->
                    <li><a href="#" class="nav-link">测试项目 1</a></li>
                    <li><a href="#" class="nav-link">测试项目 2</a></li>
                    <li><a href="#" class="nav-link">测试项目 3</a></li>
                    <li><a href="#" class="nav-link">测试项目 4</a></li>
                    <li><a href="#" class="nav-link">测试项目 5</a></li>
                    <li><a href="#" class="nav-link">测试项目 6</a></li>
                    <li><a href="#" class="nav-link">测试项目 7</a></li>
                    <li><a href="#" class="nav-link">测试项目 8</a></li>
                    <li><a href="#" class="nav-link">测试项目 9</a></li>
                    <li><a href="#" class="nav-link">测试项目 10</a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <p>&copy; 2024 无滚动条测试</p>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <h1>无滚动条测试</h1>
                <div class="user-info">
                    <span>测试用户</span>
                </div>
            </div>

            <!-- 内容主体 -->
            <div class="content-body">
                <div class="card">
                    <div class="card-header">
                        <h2>滚动条隐藏测试</h2>
                    </div>
                    <div class="card-body">
                        <p>这个页面用于测试滚动条是否已经被成功隐藏。</p>
                        <h3>测试区域：</h3>
                        <ul style="margin: 20px 0; padding-left: 20px;">
                            <li><strong>侧边栏</strong>: 左侧导航栏（添加了额外项目来测试滚动）</li>
                            <li><strong>表格容器</strong>: 下方的宽表格</li>
                            <li><strong>页面整体</strong>: 整个页面的滚动</li>
                        </ul>
                        <p><strong>预期效果</strong>: 所有区域都应该可以滚动，但不显示滚动条。</p>
                    </div>
                </div>

                <!-- 宽表格测试 -->
                <div class="card">
                    <div class="card-header">
                        <h2>宽表格滚动测试</h2>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>列1</th>
                                        <th>列2</th>
                                        <th>列3</th>
                                        <th>列4</th>
                                        <th>列5</th>
                                        <th>列6</th>
                                        <th>列7</th>
                                        <th>列8</th>
                                        <th>列9</th>
                                        <th>列10</th>
                                        <th>列11</th>
                                        <th>列12</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>数据1</td>
                                        <td>数据2</td>
                                        <td>数据3</td>
                                        <td>数据4</td>
                                        <td>数据5</td>
                                        <td>数据6</td>
                                        <td>数据7</td>
                                        <td>数据8</td>
                                        <td>数据9</td>
                                        <td>数据10</td>
                                        <td>数据11</td>
                                        <td>数据12</td>
                                    </tr>
                                    <tr>
                                        <td>测试A</td>
                                        <td>测试B</td>
                                        <td>测试C</td>
                                        <td>测试D</td>
                                        <td>测试E</td>
                                        <td>测试F</td>
                                        <td>测试G</td>
                                        <td>测试H</td>
                                        <td>测试I</td>
                                        <td>测试J</td>
                                        <td>测试K</td>
                                        <td>测试L</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p style="margin-top: 20px;"><em>这个表格应该可以水平滚动，但不显示滚动条。</em></p>
                    </div>
                </div>

                <!-- 添加更多内容来测试页面滚动 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>测试1</h3>
                        <div class="stat-value">✓</div>
                        <div class="stat-label">侧边栏滚动</div>
                    </div>
                    <div class="stat-card">
                        <h3>测试2</h3>
                        <div class="stat-value">✓</div>
                        <div class="stat-label">表格滚动</div>
                    </div>
                    <div class="stat-card">
                        <h3>测试3</h3>
                        <div class="stat-value">✓</div>
                        <div class="stat-label">页面滚动</div>
                    </div>
                    <div class="stat-card">
                        <h3>测试4</h3>
                        <div class="stat-value">✓</div>
                        <div class="stat-label">无滚动条</div>
                    </div>
                </div>

                <!-- 更多内容 -->
                <div class="card">
                    <div class="card-header">
                        <h2>更多测试内容</h2>
                    </div>
                    <div class="card-body">
                        <p>这里是更多的测试内容，用于确保页面有足够的高度来测试垂直滚动。</p>
                        <div style="height: 200px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); margin: 20px 0; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <span>占位内容区域</span>
                        </div>
                        <p>滚动条应该已经被完全隐藏，但滚动功能仍然正常工作。</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2>最终测试</h2>
                    </div>
                    <div class="card-body">
                        <p>如果你能看到这个内容，说明页面滚动正常工作。</p>
                        <div style="height: 300px; background: linear-gradient(135deg, #fafafa, #f5f5f5); margin: 20px 0; border-radius: 8px; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                            <h3>滚动条隐藏成功！</h3>
                            <p>页面可以正常滚动，但没有可见的滚动条</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示滚动状态信息
        function showScrollInfo() {
            const info = document.createElement('div');
            info.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: monospace;
                z-index: 9999;
                font-size: 12px;
                max-width: 300px;
            `;
            
            const sidebar = document.querySelector('.sidebar');
            const tableContainer = document.querySelector('.table-container');
            
            info.innerHTML = `
                <strong>滚动状态检测:</strong><br>
                页面高度: ${document.body.scrollHeight}px<br>
                窗口高度: ${window.innerHeight}px<br>
                可滚动: ${document.body.scrollHeight > window.innerHeight ? '是' : '否'}<br>
                <br>
                侧边栏可滚动: ${sidebar.scrollHeight > sidebar.clientHeight ? '是' : '否'}<br>
                表格可滚动: ${tableContainer.scrollWidth > tableContainer.clientWidth ? '是' : '否'}<br>
                <br>
                <em>滚动条应该已被隐藏</em>
            `;
            
            document.body.appendChild(info);
            
            // 10秒后移除
            setTimeout(() => {
                if (document.body.contains(info)) {
                    document.body.removeChild(info);
                }
            }, 10000);
        }

        // 页面加载完成后显示信息
        window.addEventListener('load', showScrollInfo);
    </script>
</body>
</html>
