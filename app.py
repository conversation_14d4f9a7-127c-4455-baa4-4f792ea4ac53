from flask import Flask, render_template, redirect, url_for, request
from flask_jwt_extended import JWTManager, jwt_required, get_jwt_identity
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import os
import logging

from models import db, User, Role
from config import config
from services.admin_service import create_default_admin

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 初始化扩展
jwt = JWTManager()
limiter = Limiter(key_func=get_remote_address)

def migrate_super_admin_to_admin():
    """将超级管理员角色的用户迁移到管理员角色"""
    try:
        # 查找超级管理员角色
        super_admin_role = Role.query.filter_by(name='super_admin').first()
        if not super_admin_role:
            logger.info("没有找到超级管理员角色，无需迁移")
            return
            
        # 查找管理员角色
        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            logger.warning("没有找到管理员角色，无法迁移")
            return
            
        # 查找所有拥有超级管理员角色的用户
        super_admin_users = super_admin_role.users
        
        count = 0
        for user in super_admin_users:
            # 添加管理员角色
            if admin_role not in user.roles:
                user.roles.append(admin_role)
                count += 1
                logger.info(f"用户 {user.username} 从超级管理员迁移到管理员角色")
        
        if count > 0:
            db.session.commit()
            logger.info(f"成功将 {count} 个超级管理员用户迁移到管理员角色")
            
        # 删除超级管理员角色
        db.session.delete(super_admin_role)
        db.session.commit()
        logger.info("已删除超级管理员角色")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"迁移超级管理员角色失败: {str(e)}")

def create_app(config_name='default'):
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    limiter.init_app(app)
    
    # 配置JWT以使用cookies
    app.config['JWT_TOKEN_LOCATION'] = ['cookies']
    app.config['JWT_COOKIE_SECURE'] = not app.config['DEBUG']  # 在生产环境中使用安全cookie
    app.config['JWT_COOKIE_CSRF_PROTECT'] = False  # 禁用JWT Cookie的CSRF保护
    app.config['JWT_COOKIE_SAMESITE'] = 'Lax'

    # JWT错误处理器
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        from utils.helpers import unauthorized
        return unauthorized("JWT token已过期")

    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        from utils.helpers import unauthorized
        return unauthorized("无效的JWT token")

    @jwt.unauthorized_loader
    def missing_token_callback(error):
        from utils.helpers import unauthorized
        return unauthorized("缺少JWT token")
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        
        # 确保基本角色存在
        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            admin_role = Role(
                name='admin',
                display_name='管理员',
                description='管理员角色'
            )
            db.session.add(admin_role)
            
        user_role = Role.query.filter_by(name='user').first()
        if not user_role:
            user_role = Role(
                name='user',
                display_name='普通用户',
                description='普通用户角色'
            )
            db.session.add(user_role)
            
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建基本角色失败: {str(e)}")
        
        # 迁移超级管理员角色到管理员角色
        migrate_super_admin_to_admin()
        
        # 创建默认管理员账户
        create_default_admin()
    
    # 注册蓝图
    from routes.auth import auth_bp
    from routes.invite import invite_bp
    from routes.api import api_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(invite_bp, url_prefix='/api/invite')
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 前端路由
    @app.route('/')
    def index():
        """前端门户首页"""
        return render_template('index.html')
    
    @app.route('/dashboard')
    @jwt_required()
    def dashboard():
        """控制台入口 - 需要JWT验证"""
        user_id = get_jwt_identity()
        # 将字符串ID转换为整数
        user = User.query.get(int(user_id))
        if not user:
            return redirect(url_for('login'))
        
        return render_template('console/dashboard.html')
    
    @app.route('/login')
    def login():
        """登录页面"""
        return render_template('console/login.html')
    
    @app.route('/change_password')
    @jwt_required()
    def change_password():
        """修改密码页面"""
        return render_template('console/change_password.html')
    
    # 错误处理
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('404.html'), 404
    
    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('500.html'), 500
    
    return app

if __name__ == '__main__':
    app = create_app(os.getenv('FLASK_CONFIG') or 'default')
    app.run(debug=True) 