import os
from datetime import timedelta

# 基础配置
class Config:
    # 应用配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-change-in-production'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    
    # 邀请码配置
    INVITE_CODE_LENGTH = 8
    INVITE_CODE_EXPIRY_DAYS = 7
    
    # 请求限制配置
    RATELIMIT_DEFAULT = "200 per day, 50 per hour"
    RATELIMIT_STORAGE_URL = "memory://"
    
    # 缓存配置
    CACHE_TYPE = "SimpleCache"
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 默认管理员账户配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin12'
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL') or '<EMAIL>'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123456'
    
    # 管理员安全配置
    ADMIN_PASSWORD_MIN_LENGTH = 10  # 管理员密码最小长度
    ADMIN_PASSWORD_REQUIRE_SPECIAL = True  # 是否要求特殊字符
    ADMIN_PASSWORD_REQUIRE_NUMBERS = True  # 是否要求数字
    ADMIN_PASSWORD_REQUIRE_UPPERCASE = True  # 是否要求大写字母
    ADMIN_PASSWORD_HISTORY_COUNT = 5  # 记住最近几次的密码，防止重复使用
    ADMIN_PASSWORD_EXPIRY_DAYS = 90  # 密码有效期（天）
    ADMIN_FORCE_FIRST_LOGIN_CHANGE = True  # 首次登录强制修改密码
    ADMIN_MAX_LOGIN_ATTEMPTS = 5  # 最大登录尝试次数
    ADMIN_ACCOUNT_LOCKOUT_MINUTES = 30  # 账户锁定时间（分钟）
    
    # 角色配置
    DEFAULT_ROLES = {
        'admin': '管理员',
        'user': '普通用户'
    }
    DEFAULT_ADMIN_ROLE = 'admin'  # 默认管理员角色
    DEFAULT_USER_ROLE = 'user'  # 默认用户角色

# 开发环境配置
class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///invite_system_dev.db'

# 测试环境配置
class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///invite_system_test.db'

# 生产环境配置
class ProductionConfig(Config):
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///invite_system.db'
    
    # 生产环境应使用环境变量设置密钥
    # 如果环境变量未设置，则使用基类中的默认值
    # 注意：在实际生产环境中，应该设置这些环境变量以增强安全性
    SECRET_KEY = os.environ.get('SECRET_KEY') or Config.SECRET_KEY
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or Config.JWT_SECRET_KEY
    
    # 生产环境中管理员账户配置也应从环境变量获取
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or Config.ADMIN_USERNAME
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL') or Config.ADMIN_EMAIL
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or Config.ADMIN_PASSWORD
    
    # 生产环境中的安全配置
    ADMIN_MAX_LOGIN_ATTEMPTS = 3  # 生产环境降低最大尝试次数
    ADMIN_ACCOUNT_LOCKOUT_MINUTES = 60  # 生产环境延长锁定时间
    ADMIN_PASSWORD_EXPIRY_DAYS = 60  # 生产环境缩短密码有效期

# 配置映射
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
} 