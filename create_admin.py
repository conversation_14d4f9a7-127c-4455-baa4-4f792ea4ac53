#!/usr/bin/env python
"""
邀请码核销系统 - 创建管理员账号脚本
使用方法：python create_admin.py <用户名> <邮箱> <密码>
"""

import sys
import os
import argparse
from flask import Flask

# 导入应用相关模块
from models import db, User
from config import config
from services.admin_service import create_admin_user, validate_admin_password

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(config['development'])
    db.init_app(app)
    return app

def update_database_schema(app):
    """更新数据库架构，添加缺少的列"""
    with app.app_context():
        print("检查数据库架构...")
        # 检查users表是否需要更新
        conn = db.engine.raw_connection()
        cursor = conn.cursor()
        
        # 获取users表的当前列
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 检查并添加缺少的列
        missing_columns = []
        if 'login_attempts' not in column_names:
            missing_columns.append(('login_attempts', 'INTEGER DEFAULT 0'))
        if 'locked_until' not in column_names:
            missing_columns.append(('locked_until', 'DATETIME'))
        if 'password_changed_at' not in column_names:
            missing_columns.append(('password_changed_at', 'DATETIME'))
        if 'force_password_change' not in column_names:
            missing_columns.append(('force_password_change', 'BOOLEAN DEFAULT 0'))
        
        # 添加缺少的列
        for column_name, column_type in missing_columns:
            print(f"添加缺少的列: {column_name} ({column_type})")
            cursor.execute(f"ALTER TABLE users ADD COLUMN {column_name} {column_type}")
        
        # 特殊处理password_changed_at列，为现有记录设置默认值
        if 'password_changed_at' in [col[0] for col in missing_columns]:
            print("为现有用户设置password_changed_at默认值...")
            cursor.execute("UPDATE users SET password_changed_at = datetime('now') WHERE password_changed_at IS NULL")
        
        conn.commit()
        conn.close()
        
        if missing_columns:
            print(f"成功添加了 {len(missing_columns)} 个缺少的列")
        else:
            print("数据库架构已是最新")

def main():
    """脚本入口函数"""
    print("开始创建管理员账号...")
    
    parser = argparse.ArgumentParser(description='创建管理员账号')
    parser.add_argument('username', help='管理员用户名')
    parser.add_argument('email', help='管理员邮箱')
    parser.add_argument('password', help='管理员密码')
    
    args = parser.parse_args()
    
    username = args.username
    email = args.email
    password = args.password
    
    print(f"参数: 用户名={username}, 邮箱={email}, 密码长度={len(password)}")
    
    app = create_app()
    
    # 更新数据库架构
    update_database_schema(app)
    
    with app.app_context():
        print("验证密码强度...")
        # 验证密码强度
        is_valid, error_msg = validate_admin_password(password)
        if not is_valid:
            print(f"密码强度不足: {error_msg}")
            sys.exit(1)
        
        print("开始创建管理员用户...")
        # 创建管理员账户
        success, result = create_admin_user(
            username=username,
            email=email,
            password=password
        )
        
        if success:
            user = result['user']
            print("管理员账号创建成功！")
            print(f"用户名: {user.username}")
            print(f"邮箱: {user.email}")
            print(f"是否管理员: {user.is_admin}")
            sys.exit(0)
        else:
            print(f"创建管理员失败: {result}")
            sys.exit(1)

if __name__ == "__main__":
    print("脚本开始执行")
    main()
    print("脚本执行完毕") 