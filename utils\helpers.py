from flask import jsonify

def api_response(data=None, message="", status_code=200, error=None):
    """
    统一API响应格式
    
    参数:
        data: 响应数据
        message: 响应消息
        status_code: HTTP状态码
        error: 错误信息
    
    返回:
        JSON格式的响应
    """
    response = {
        "success": error is None,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
    
    if error is not None:
        response["error"] = error
    
    return jsonify(response), status_code

def error_response(message, code=400, error_details=None):
    """
    生成错误响应
    
    参数:
        message: 错误消息
        code: HTTP状态码
        error_details: 详细错误信息
    
    返回:
        JSON格式的错误响应
    """
    return api_response(
        message=message,
        status_code=code,
        error=error_details or message
    )

# 常见错误响应
def bad_request(message="请求参数错误"):
    return error_response(message, 400)

def unauthorized(message="未授权访问"):
    return error_response(message, 401)

def forbidden(message="禁止访问"):
    return error_response(message, 403)

def not_found(message="资源不存在"):
    return error_response(message, 404)

def validation_error(errors):
    """
    表单验证错误响应
    
    参数:
        errors: 验证错误信息
    
    返回:
        JSON格式的验证错误响应
    """
    return error_response("表单验证失败", 422, errors) 