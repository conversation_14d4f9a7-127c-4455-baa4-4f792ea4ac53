# 创建邀请码蓝图
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import desc
from datetime import datetime, timedelta
import threading

from models import db, InviteCode, User
from utils.helpers import api_response, bad_request, unauthorized, not_found, forbidden

invite_bp = Blueprint('invite', __name__)

# 创建一个线程锁，用于防止邀请码重复核销
redeem_lock = threading.Lock()

@invite_bp.route('/generate', methods=['POST'])
@jwt_required()
def generate_invite():
    """生成邀请码（支持批量生成）"""
    user_id = get_jwt_identity()
    # 将字符串ID转换为整数
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    # 从请求中获取参数
    data = request.get_json() or {}
    expiry_days = data.get('expiry_days', current_app.config['INVITE_CODE_EXPIRY_DAYS'])
    count = data.get('count', 1)  # 默认生成1个
    
    # 验证生成数量
    if not isinstance(count, int) or count < 1:
        return bad_request("生成数量必须是正整数")
    
    # 限制单次批量生成数量
    max_batch_count = 50 if user.is_admin else 20
    if count > max_batch_count:
        return bad_request(f"单次最多可生成{max_batch_count}个邀请码")
    
    # 检查未使用邀请码数量限制（仅对非管理员）
    if not user.is_admin:
        active_codes = InviteCode.query.filter_by(
            creator_id=int(user_id), 
            is_used=False
        ).filter(
            InviteCode.expires_at > datetime.utcnow()
        ).count()
        
        max_active_codes = 50  # 普通用户最多50个未使用邀请码
        if active_codes + count > max_active_codes:
            return forbidden(f"您的未使用邀请码数量将超过限制（{max_active_codes}个）")
    
    try:
        # 批量创建邀请码
        generated_codes = []
        for _ in range(count):
            invite = InviteCode.create_code(int(user_id), expiry_days)
            db.session.add(invite)
            generated_codes.append(invite)
        
        # 提交事务
        db.session.commit()
        
        # 构建响应数据
        if count == 1:
            # 单个生成，保持原有响应格式
            return api_response(
                data=generated_codes[0].to_dict(),
                message="邀请码生成成功",
                status_code=201
            )
        else:
            # 批量生成，返回所有生成的邀请码
            return api_response(
                data={
                    'codes': [code.to_dict() for code in generated_codes],
                    'count': count,
                    'expiry_days': expiry_days
                },
                message=f"成功生成{count}个邀请码",
                status_code=201
            )
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量生成邀请码失败: {str(e)}")
        return bad_request("生成邀请码失败，请稍后重试")

@invite_bp.route('/redeem', methods=['POST'])
@jwt_required()
def redeem_invite():
    """核销邀请码"""
    user_id = get_jwt_identity()
    # 将字符串ID转换为整数
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    data = request.get_json()
    
    if not data or 'code' not in data:
        return bad_request("请提供邀请码")
    
    invite_code = data['code']
    
    # 使用线程锁防止并发核销
    with redeem_lock:
        try:
            # 开始数据库事务
            # 查找邀请码并加锁
            invite = InviteCode.query.filter_by(code=invite_code).with_for_update().first()
            
            if not invite:
                return not_found("邀请码不存在")
            
            # 验证邀请码
            if invite.is_used:
                return bad_request("邀请码已被使用")
            
            if invite.expires_at < datetime.utcnow():
                return bad_request("邀请码已过期")
            
            # 核销邀请码
            if not invite.redeem(int(user_id)):
                return bad_request("邀请码无效")
            
            # 提交事务
            db.session.commit()
            
            return api_response(
                data=invite.to_dict(),
                message="邀请码核销成功"
            )
        except Exception as e:
            # 回滚事务
            db.session.rollback()
            current_app.logger.error(f"邀请码核销失败: {str(e)}")
            return bad_request("邀请码核销失败，请稍后重试")

@invite_bp.route('/codes', methods=['GET'])
@jwt_required()
def get_invite_codes():
    """获取邀请码列表"""
    user_id = get_jwt_identity()
    # 将字符串ID转换为整数
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    # 查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 50)  # 限制每页最多50条
    status = request.args.get('status')  # 可选：all, used, unused, expired
    api_key_filter = request.args.get('api_key')  # 可选：API Key ID或'system'

    # 构建查询
    query = InviteCode.query

    # 非管理员只能查看自己的邀请码
    if not user.is_admin:
        query = query.filter(
            (InviteCode.creator_id == int(user_id)) |
            (InviteCode.used_by_id == int(user_id))
        )

    # 根据API Key过滤
    if api_key_filter:
        if api_key_filter == 'system':
            # 系统生成的邀请码（没有关联API Key）
            query = query.filter(InviteCode.api_key_id.is_(None))
        else:
            # 特定API Key生成的邀请码
            try:
                api_key_id = int(api_key_filter)
                query = query.filter_by(api_key_id=api_key_id)
            except ValueError:
                return bad_request("无效的API Key ID")

    # 根据状态过滤
    if status == 'used':
        query = query.filter_by(is_used=True)
    elif status == 'unused':
        query = query.filter_by(is_used=False)
    elif status == 'expired':
        query = query.filter(InviteCode.expires_at < datetime.utcnow())
    
    # 分页
    pagination = query.order_by(desc(InviteCode.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    codes = pagination.items
    
    return api_response(
        data={
            'codes': [code.to_dict() for code in codes],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page
        },
        message="获取邀请码列表成功"
    )

@invite_bp.route('/codes/<code>', methods=['GET'])
@jwt_required()
def get_invite_code(code):
    """获取单个邀请码详情"""
    user_id = get_jwt_identity()
    # 将字符串ID转换为整数
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    # 查找邀请码
    invite = InviteCode.query.filter_by(code=code).first()
    
    if not invite:
        return not_found("邀请码不存在")
    
    # 非管理员只能查看自己的邀请码
    if not user.is_admin and invite.creator_id != int(user_id) and invite.used_by_id != int(user_id):
        return forbidden("您无权查看此邀请码")
    
    return api_response(
        data=invite.to_dict(),
        message="获取邀请码详情成功"
    )

@invite_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_invite_stats():
    """获取邀请码统计数据"""
    user_id = get_jwt_identity()
    # 将字符串ID转换为整数
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    # 构建基础查询
    base_query = InviteCode.query
    
    # 非管理员只能查看自己的邀请码
    if not user.is_admin:
        base_query = base_query.filter(
            (InviteCode.creator_id == int(user_id)) | 
            (InviteCode.used_by_id == int(user_id))
        )
    
    # 获取统计数据
    total_codes = base_query.count()
    used_codes = base_query.filter_by(is_used=True).count()
    unused_codes = base_query.filter_by(is_used=False).count()
    expired_codes = base_query.filter(
        InviteCode.expires_at < datetime.utcnow(),
        InviteCode.is_used == False
    ).count()
    
    # 计算使用率
    usage_rate = round((used_codes / total_codes * 100) if total_codes > 0 else 0, 1)
    
    return api_response(
        data={
            'total_codes': total_codes,
            'used_codes': used_codes,
            'unused_codes': unused_codes,
            'expired_codes': expired_codes,
            'usage_rate': usage_rate
        },
        message="获取统计数据成功"
    )

@invite_bp.route('/codes/<int:code_id>', methods=['DELETE'])
@jwt_required()
def delete_invite_code(code_id):
    """删除单个邀请码"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    # 查找邀请码
    invite = InviteCode.query.get(code_id)
    
    if not invite:
        return not_found("邀请码不存在")
    
    # 权限检查：只允许创建者或管理员删除
    if not user.is_admin and invite.creator_id != int(user_id):
        return forbidden("您无权删除此邀请码")
    
    # 业务规则：不允许删除已使用的邀请码
    if invite.is_used:
        return bad_request("不能删除已使用的邀请码")
    
    try:
        db.session.delete(invite)
        db.session.commit()
        
        return api_response(
            message="邀请码删除成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除邀请码失败: {str(e)}")
        return bad_request("删除邀请码失败，请稍后重试")

@invite_bp.route('/codes/batch', methods=['DELETE'])
@jwt_required()
def batch_delete_invite_codes():
    """批量删除邀请码"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    data = request.get_json()
    
    if not data or 'code_ids' not in data:
        return bad_request("请提供要删除的邀请码ID列表")
    
    code_ids = data['code_ids']
    
    if not isinstance(code_ids, list) or len(code_ids) == 0:
        return bad_request("邀请码ID列表不能为空")
    
    if len(code_ids) > 50:  # 限制批量删除数量
        return bad_request("单次批量删除最多支持50个邀请码")
    
    try:
        # 查找所有要删除的邀请码
        invites = InviteCode.query.filter(InviteCode.id.in_(code_ids)).all()
        
        if not invites:
            return not_found("没有找到要删除的邀请码")
        
        # 权限和业务规则检查
        deleted_count = 0
        error_messages = []
        
        for invite in invites:
            # 权限检查
            if not user.is_admin and invite.creator_id != int(user_id):
                error_messages.append(f"邀请码 {invite.code}: 无权删除")
                continue
            
            # 业务规则检查
            if invite.is_used:
                error_messages.append(f"邀请码 {invite.code}: 已使用，不能删除")
                continue
            
            # 删除邀请码
            db.session.delete(invite)
            deleted_count += 1
        
        db.session.commit()
        
        # 构建响应消息
        message = f"成功删除 {deleted_count} 个邀请码"
        if error_messages:
            message += f"，{len(error_messages)} 个邀请码删除失败"
        
        return api_response(
            data={
                'deleted_count': deleted_count,
                'failed_count': len(error_messages),
                'errors': error_messages
            },
            message=message
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量删除邀请码失败: {str(e)}")
        return bad_request("批量删除邀请码失败，请稍后重试")

@invite_bp.route('/redeem/<int:code_id>', methods=['DELETE'])
@jwt_required()
def delete_redeem_record(code_id):
    """删除核销记录（重置邀请码为未使用状态）"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))

    if not user:
        return not_found("用户不存在")

    # 查找邀请码
    invite = InviteCode.query.get(code_id)

    if not invite:
        return not_found("邀请码不存在")

    # 权限检查：只允许创建者或管理员删除核销记录
    if not user.is_admin and invite.creator_id != int(user_id):
        return forbidden("您无权删除此核销记录")

    # 业务规则：只能删除已使用的邀请码的核销记录
    if not invite.is_used:
        return bad_request("该邀请码未被使用，无法删除核销记录")

    try:
        # 重置邀请码状态
        invite.is_used = False
        invite.used_by_id = None
        invite.redeemed_at = None

        db.session.commit()

        return api_response(
            message="核销记录删除成功，邀请码已重置为未使用状态"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除核销记录失败: {str(e)}")
        return bad_request("删除核销记录失败，请稍后重试")

@invite_bp.route('/redeem/batch', methods=['DELETE'])
@jwt_required()
def batch_delete_redeem_records():
    """批量删除核销记录（重置邀请码为未使用状态）"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))

    if not user:
        return not_found("用户不存在")

    data = request.get_json()
    if not data or 'code_ids' not in data:
        return bad_request("请提供要删除的核销记录ID列表")

    code_ids = data['code_ids']
    if not isinstance(code_ids, list) or len(code_ids) == 0:
        return bad_request("核销记录ID列表不能为空")

    if len(code_ids) > 100:
        return bad_request("单次最多删除100个核销记录")

    try:
        # 查找所有要删除的邀请码
        invites = InviteCode.query.filter(InviteCode.id.in_(code_ids)).all()

        if not invites:
            return not_found("没有找到要删除的核销记录")

        # 权限和业务规则检查
        reset_count = 0
        error_messages = []

        for invite in invites:
            # 权限检查
            if not user.is_admin and invite.creator_id != int(user_id):
                error_messages.append(f"邀请码 {invite.code}: 无权删除核销记录")
                continue

            # 业务规则检查
            if not invite.is_used:
                error_messages.append(f"邀请码 {invite.code}: 未被使用，无法删除核销记录")
                continue

            # 重置邀请码状态
            invite.is_used = False
            invite.used_by_id = None
            invite.redeemed_at = None
            reset_count += 1

        db.session.commit()

        # 构建响应消息
        message = f"成功删除 {reset_count} 个核销记录"
        if error_messages:
            message += f"，{len(error_messages)} 个记录删除失败"

        return api_response(
            data={
                'reset_count': reset_count,
                'failed_count': len(error_messages),
                'errors': error_messages
            },
            message=message
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量删除核销记录失败: {str(e)}")
        return bad_request("批量删除核销记录失败，请稍后重试")