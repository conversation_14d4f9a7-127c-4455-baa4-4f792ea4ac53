<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key批量生成演示 - 邀请码核销系统</title>
    <link rel="stylesheet" href="static/css/modern-console.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>邀请码核销系统</h2>
                <p>API批量生成演示</p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="#api-keys" class="nav-link active">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                            </svg>
                            API 管理
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <p>&copy; 2024 API批量生成演示</p>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <h1>API Key批量生成演示</h1>
                <div class="user-info">
                    <span>演示用户</span>
                </div>
            </div>

            <!-- 内容主体 -->
            <div class="content-body">
                <!-- API Key列表 -->
                <div class="card">
                    <div class="card-header">
                        <h2>API Key列表</h2>
                        <button class="btn btn-primary" onclick="showToast('这是演示页面，创建功能未实现', 'info')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                            创建API Key
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>API Key</th>
                                        <th>状态</th>
                                        <th>邀请码数量</th>
                                        <th>创建时间</th>
                                        <th>最后使用</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>我的应用</td>
                                        <td><span class="code-display">ag-demo1234567890ab</span></td>
                                        <td><span class="badge badge-success">活跃</span></td>
                                        <td>25</td>
                                        <td>2024-01-15 10:30</td>
                                        <td>2024-01-16 14:20</td>
                                        <td class="actions">
                                            <button class="btn btn-sm btn-primary" onclick="showApiBatchGenerateModal(1, '我的应用', 'ag-demo1234567890ab')">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <line x1="12" y1="8" x2="12" y2="16"></line>
                                                    <line x1="8" y1="12" x2="16" y2="12"></line>
                                                </svg>
                                                批量生成
                                            </button>
                                            <button class="btn btn-sm btn-outline" onclick="showToast('这是演示页面', 'info')">查看</button>
                                            <button class="btn btn-sm btn-secondary" onclick="showToast('这是演示页面', 'info')">禁用</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>测试应用</td>
                                        <td><span class="code-display">ag-test9876543210cd</span></td>
                                        <td><span class="badge badge-success">活跃</span></td>
                                        <td>12</td>
                                        <td>2024-01-14 16:45</td>
                                        <td>2024-01-15 09:15</td>
                                        <td class="actions">
                                            <button class="btn btn-sm btn-primary" onclick="showApiBatchGenerateModal(2, '测试应用', 'ag-test9876543210cd')">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <line x1="12" y1="8" x2="12" y2="16"></line>
                                                    <line x1="8" y1="12" x2="16" y2="12"></line>
                                                </svg>
                                                批量生成
                                            </button>
                                            <button class="btn btn-sm btn-outline" onclick="showToast('这是演示页面', 'info')">查看</button>
                                            <button class="btn btn-sm btn-secondary" onclick="showToast('这是演示页面', 'info')">禁用</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>生产环境</td>
                                        <td><span class="code-display">ag-prod5555666677ef</span></td>
                                        <td><span class="badge badge-warning">已禁用</span></td>
                                        <td>0</td>
                                        <td>2024-01-10 12:00</td>
                                        <td>从未使用</td>
                                        <td class="actions">
                                            <button class="btn btn-sm btn-primary" disabled title="API Key已禁用">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <line x1="12" y1="8" x2="12" y2="16"></line>
                                                    <line x1="8" y1="12" x2="16" y2="12"></line>
                                                </svg>
                                                批量生成
                                            </button>
                                            <button class="btn btn-sm btn-outline" onclick="showToast('这是演示页面', 'info')">查看</button>
                                            <button class="btn btn-sm btn-primary" onclick="showToast('这是演示页面', 'info')">启用</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 功能说明 -->
                <div class="card">
                    <div class="card-header">
                        <h2>批量生成功能说明</h2>
                    </div>
                    <div class="card-body">
                        <h3>功能特点：</h3>
                        <ul style="margin: 20px 0; padding-left: 20px; line-height: 1.8;">
                            <li><strong>批量生成</strong>：支持一次性生成1-100个邀请码</li>
                            <li><strong>自定义有效期</strong>：可设置1-365天的有效期</li>
                            <li><strong>API Key关联</strong>：生成的邀请码自动关联到指定的API Key</li>
                            <li><strong>结果展示</strong>：生成后在模态框中展示所有邀请码</li>
                            <li><strong>便捷操作</strong>：支持一键复制和下载邀请码列表</li>
                            <li><strong>自动下载</strong>：可选择生成后自动下载txt文件</li>
                        </ul>
                        
                        <h3>使用方法：</h3>
                        <ol style="margin: 20px 0; padding-left: 20px; line-height: 1.8;">
                            <li>在API Key列表中找到要使用的API Key</li>
                            <li>点击对应行的"批量生成"按钮</li>
                            <li>在弹出的模态框中设置生成数量和有效期</li>
                            <li>选择是否自动下载邀请码列表</li>
                            <li>点击"开始生成"按钮</li>
                            <li>生成完成后可以复制或下载邀请码</li>
                        </ol>
                        
                        <p style="margin-top: 20px; padding: 15px; background: var(--bg-tertiary); border-radius: var(--radius-md); color: var(--text-secondary);">
                            <strong>提示</strong>：点击上方表格中的"批量生成"按钮来体验完整的批量生成流程。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Key批量生成邀请码模态框 -->
    <div id="api-batch-generate-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量生成邀请码</h3>
                <button class="modal-close" onclick="closeApiBatchGenerateModal()">×</button>
            </div>
            <div class="modal-body">
                <form id="api-batch-generate-form" onsubmit="handleApiBatchGenerate(event)">
                    <input type="hidden" id="api-batch-key-id" name="api_key_id">
                    <input type="hidden" id="api-batch-key-name" name="api_key_name">
                    
                    <div class="form-group">
                        <label>API Key信息</label>
                        <div style="padding: 12px; background: var(--bg-tertiary); border-radius: var(--radius-md); margin-bottom: 16px;">
                            <div style="font-weight: 600; margin-bottom: 4px;">名称: <span id="api-batch-display-name">-</span></div>
                            <div style="font-family: var(--font-mono); font-size: var(--text-sm); color: var(--text-secondary);">
                                Key: <span id="api-batch-display-key">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="api-batch-count">生成数量</label>
                            <input type="number" id="api-batch-count" name="count" class="form-control" min="1" max="100" value="10" required>
                            <small class="form-text">单次最多生成100个邀请码</small>
                        </div>
                        <div class="form-group">
                            <label for="api-batch-expiry">有效期（天）</label>
                            <input type="number" id="api-batch-expiry" name="expiry_days" class="form-control" min="1" max="365" value="7" required>
                            <small class="form-text">邀请码的有效期，1-365天</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="api-batch-auto-download" name="auto_download" checked>
                            生成后自动下载邀请码列表
                        </label>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeApiBatchGenerateModal()">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                            开始生成
                        </button>
                    </div>
                </form>
                
                <!-- 批量生成结果展示 -->
                <div id="api-batch-result" class="batch-result" style="display: none;">
                    <div class="batch-result-header">
                        <h4>批量生成结果</h4>
                        <div class="batch-result-actions">
                            <button id="api-batch-copy-all" class="btn btn-sm btn-secondary">复制全部</button>
                            <button id="api-batch-download" class="btn btn-sm btn-primary">下载列表</button>
                            <button id="api-batch-close-result" class="btn btn-sm btn-secondary">关闭</button>
                        </div>
                    </div>
                    <div class="batch-result-content">
                        <div class="batch-result-summary">
                            为 API Key "<span id="api-batch-result-key-name">-</span>" 成功生成 <span id="api-batch-result-count">0</span> 个邀请码
                        </div>
                        <div class="batch-codes-container">
                            <div id="api-batch-codes-list" class="batch-codes-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟生成邀请码的函数
        function generateRandomCode() {
            const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
            let result = '';
            for (let i = 0; i < 8; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // 显示API Key批量生成模态框
        function showApiBatchGenerateModal(apiKeyId, apiKeyName, apiKey) {
            document.getElementById('api-batch-key-id').value = apiKeyId;
            document.getElementById('api-batch-key-name').value = apiKeyName;
            document.getElementById('api-batch-display-name').textContent = apiKeyName;
            document.getElementById('api-batch-display-key').textContent = apiKey;
            
            // 重置表单
            document.getElementById('api-batch-generate-form').reset();
            document.getElementById('api-batch-key-id').value = apiKeyId;
            document.getElementById('api-batch-key-name').value = apiKeyName;
            document.getElementById('api-batch-count').value = 10;
            document.getElementById('api-batch-expiry').value = 7;
            document.getElementById('api-batch-auto-download').checked = true;
            
            // 隐藏结果区域
            document.getElementById('api-batch-result').style.display = 'none';
            
            // 显示模态框
            document.getElementById('api-batch-generate-modal').style.display = 'flex';
        }

        // 关闭API Key批量生成模态框
        function closeApiBatchGenerateModal() {
            document.getElementById('api-batch-generate-modal').style.display = 'none';
            document.getElementById('api-batch-generate-form').reset();
            document.getElementById('api-batch-result').style.display = 'none';
        }

        // 处理批量生成表单提交
        function handleApiBatchGenerate(event) {
            event.preventDefault();
            
            const count = parseInt(document.getElementById('api-batch-count').value);
            const expiryDays = parseInt(document.getElementById('api-batch-expiry').value);
            const autoDownload = document.getElementById('api-batch-auto-download').checked;
            const apiKeyName = document.getElementById('api-batch-key-name').value;
            
            // 显示加载状态
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<div class="spinner-border spinner-border-sm"></div> 生成中...';
            submitBtn.disabled = true;
            
            // 模拟API调用延迟
            setTimeout(() => {
                // 生成模拟邀请码
                const codes = [];
                for (let i = 0; i < count; i++) {
                    codes.push(generateRandomCode());
                }
                
                // 显示结果
                showApiBatchResult(codes, apiKeyName, autoDownload);
                
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                showToast(`成功生成 ${count} 个邀请码`, 'success');
            }, 1500);
        }

        // 显示批量生成结果
        function showApiBatchResult(codes, apiKeyName, autoDownload) {
            document.getElementById('api-batch-result-key-name').textContent = apiKeyName;
            document.getElementById('api-batch-result-count').textContent = codes.length;
            
            const codesList = document.getElementById('api-batch-codes-list');
            codesList.innerHTML = '';
            
            codes.forEach((code, index) => {
                const codeItem = document.createElement('div');
                codeItem.className = 'batch-code-item';
                codeItem.innerHTML = `
                    <span class="batch-code-text">${code}</span>
                    <button class="btn btn-sm btn-outline" onclick="copyCode('${code}')">复制</button>
                `;
                codesList.appendChild(codeItem);
            });
            
            document.getElementById('api-batch-result').style.display = 'block';
            
            // 自动下载
            if (autoDownload) {
                setTimeout(() => {
                    downloadApiBatchCodes();
                }, 500);
            }
        }

        // 复制单个邀请码
        function copyCode(code) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(code).then(() => {
                    showToast(`已复制邀请码: ${code}`, 'success');
                });
            } else {
                fallbackCopyText(code);
            }
        }

        // 复制所有邀请码
        function copyApiBatchCodes() {
            const codeElements = document.querySelectorAll('#api-batch-codes-list .batch-code-text');
            const codes = Array.from(codeElements).map(el => el.textContent).join('\n');
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(codes).then(() => {
                    showToast('已复制所有邀请码到剪贴板', 'success');
                }).catch(() => {
                    fallbackCopyText(codes);
                });
            } else {
                fallbackCopyText(codes);
            }
        }

        // 下载邀请码列表
        function downloadApiBatchCodes() {
            const codeElements = document.querySelectorAll('#api-batch-codes-list .batch-code-text');
            const codes = Array.from(codeElements).map(el => el.textContent);
            const apiKeyName = document.getElementById('api-batch-result-key-name').textContent;
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            
            const content = codes.join('\n');
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `invite-codes-${apiKeyName}-${timestamp}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showToast('邀请码列表已下载', 'success');
        }

        // 备用复制方法
        function fallbackCopyText(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showToast('已复制到剪贴板', 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
            document.body.removeChild(textArea);
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-out forwards';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定事件监听器
            document.getElementById('api-batch-copy-all').addEventListener('click', copyApiBatchCodes);
            document.getElementById('api-batch-download').addEventListener('click', downloadApiBatchCodes);
            document.getElementById('api-batch-close-result').addEventListener('click', function() {
                document.getElementById('api-batch-result').style.display = 'none';
            });
            
            // 显示欢迎消息
            setTimeout(() => {
                showToast('欢迎使用API Key批量生成功能演示！', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
