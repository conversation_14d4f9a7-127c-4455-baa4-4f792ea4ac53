#!/usr/bin/env python
"""
检查数据库表脚本
"""

import sqlite3

def check_tables():
    """检查数据库表"""
    conn = sqlite3.connect('invite_system_dev.db')
    cursor = conn.cursor()
    
    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("数据库中的表:")
    for table in tables:
        print(f"- {table[0]}")
    
    # 检查API Key表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys';")
    api_key_table = cursor.fetchone()
    
    if api_key_table:
        print("\nAPI Key表存在，表结构:")
        cursor.execute("PRAGMA table_info(api_keys);")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
    else:
        print("\nAPI Key表不存在")
    
    # 检查邀请码表的结构
    cursor.execute("PRAGMA table_info(invite_codes);")
    columns = cursor.fetchall()
    print("\n邀请码表结构:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    conn.close()

if __name__ == '__main__':
    check_tables() 