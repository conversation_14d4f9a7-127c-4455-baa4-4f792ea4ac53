from datetime import datetime, timedelta
from sqlalchemy import func
import threading
import random
import string

from models import db, InviteCode, User

# 用于防止重复核销的锁
redeem_lock = threading.Lock()

class InviteCodeService:
    """邀请码服务类"""
    
    @staticmethod
    def generate_code(creator_id, expiry_days=7):
        """
        生成新的邀请码
        
        参数:
            creator_id: 创建者ID
            expiry_days: 有效期天数
        
        返回:
            生成的邀请码对象
        """
        # 检查用户是否存在
        user = User.query.get(creator_id)
        if not user:
            raise ValueError("用户不存在")
        
        # 创建邀请码
        invite = InviteCode.create_code(creator_id, expiry_days)
        
        # 保存到数据库
        db.session.add(invite)
        db.session.commit()
        
        return invite
    
    @staticmethod
    def validate_code(code):
        """
        验证邀请码是否有效
        
        参数:
            code: 邀请码字符串
        
        返回:
            (bool, str): (是否有效, 错误消息)
        """
        # 查找邀请码
        invite = InviteCode.query.filter_by(code=code).first()
        
        if not invite:
            return False, "邀请码不存在"
        
        if invite.is_used:
            return False, "邀请码已被使用"
        
        if invite.expires_at < datetime.utcnow():
            return False, "邀请码已过期"
        
        return True, ""
    
    @staticmethod
    def redeem_code(code, user_id):
        """
        核销邀请码
        
        参数:
            code: 邀请码字符串
            user_id: 用户ID
        
        返回:
            (bool, str): (是否成功, 错误消息)
        """
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return False, "用户不存在"
        
        # 使用锁防止重复核销
        with redeem_lock:
            # 查找邀请码
            invite = InviteCode.query.filter_by(code=code).first()
            
            if not invite:
                return False, "邀请码不存在"
            
            # 验证邀请码
            is_valid, error = InviteCodeService.validate_code(code)
            if not is_valid:
                return False, error
            
            # 核销邀请码
            invite.is_used = True
            invite.used_by_id = user_id
            invite.redeemed_at = datetime.utcnow()
            
            db.session.commit()
            
            return True, "邀请码核销成功"
    
    @staticmethod
    def get_user_codes(user_id, include_used=True, include_expired=False):
        """
        获取用户的邀请码
        
        参数:
            user_id: 用户ID
            include_used: 是否包含已使用的邀请码
            include_expired: 是否包含已过期的邀请码
        
        返回:
            邀请码列表
        """
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        # 构建查询
        query = InviteCode.query.filter_by(creator_id=user_id)
        
        if not include_used:
            query = query.filter_by(is_used=False)
        
        if not include_expired:
            query = query.filter(InviteCode.expires_at >= datetime.utcnow())
        
        return query.all()
    
    @staticmethod
    def batch_generate_codes(creator_id, count=10, expiry_days=7):
        """
        批量生成邀请码
        
        参数:
            creator_id: 创建者ID
            count: 生成数量
            expiry_days: 有效期天数
        
        返回:
            生成的邀请码列表
        """
        # 检查用户是否存在
        user = User.query.get(creator_id)
        if not user:
            raise ValueError("用户不存在")
        
        # 批量生成邀请码
        invites = []
        for _ in range(count):
            invite = InviteCode.create_code(creator_id, expiry_days)
            invites.append(invite)
        
        # 批量保存到数据库
        db.session.add_all(invites)
        db.session.commit()
        
        return invites 