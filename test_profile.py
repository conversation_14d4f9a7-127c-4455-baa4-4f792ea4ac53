import requests

# 登录获取token
login_response = requests.post(
    'http://127.0.0.1:5000/api/auth/login',
    json={'username': 'admin', 'password': 'password123'}
)

print("登录状态码:", login_response.status_code)
print("登录响应:", login_response.text)

if login_response.status_code == 200:
    data = login_response.json()
    if data.get('success'):
        token = data['data']['access_token']
        print("\n获取到的token:", token)
        
        # 获取用户配置文件
        print("\n正在获取用户配置文件...")
        profile_response = requests.get(
            'http://127.0.0.1:5000/api/auth/profile',
            headers={'Authorization': f'Bearer {token}'}
        )
        
        print("获取配置文件状态码:", profile_response.status_code)
        print("获取配置文件响应:", profile_response.text)
    else:
        print("登录失败:", data.get('error'))
else:
    print("登录请求失败") 