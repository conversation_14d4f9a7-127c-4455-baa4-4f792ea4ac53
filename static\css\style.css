/* 全局样式 */
:root {
    --color-primary: #000000;
    --color-secondary: #333333;
    --color-light: #f5f5f5;
    --color-gray: #e0e0e0;
    --color-dark-gray: #888888;
    --color-white: #ffffff;
    --font-sans: 'Helvetica Neue', Arial, sans-serif;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    color: var(--color-primary);
    background-color: var(--color-white);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--color-secondary);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--color-primary);
}

/* 按钮样式 */
.btn-primary {
    display: inline-block;
    background-color: var(--color-primary);
    color: var(--color-white);
    padding: 12px 30px;
    border-radius: 4px;
    font-weight: 600;
    transition: var(--transition);
    border: 2px solid var(--color-primary);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--color-primary);
}

.btn-login {
    display: inline-block;
    background-color: transparent;
    color: var(--color-primary);
    padding: 8px 20px;
    border-radius: 4px;
    font-weight: 600;
    border: 2px solid var(--color-primary);
    transition: var(--transition);
}

.btn-login:hover {
    background-color: var(--color-primary);
    color: var(--color-white);
}

/* 头部导航 */
header {
    padding: 20px 0;
    background-color: var(--color-white);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

nav ul {
    display: flex;
    align-items: center;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    font-weight: 500;
}

/* 英雄区域 */
.hero {
    padding: 80px 0;
    background-color: var(--color-light);
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.hero-content h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--color-secondary);
}

/* 功能区域 */
.features {
    padding: 80px 0;
    background-color: var(--color-white);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: var(--color-light);
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 20px;
}

.feature-icon svg {
    width: 60px;
    height: 60px;
    stroke: var(--color-primary);
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.feature-card p {
    color: var(--color-secondary);
}

/* 关于区域 */
.about {
    padding: 80px 0;
    background-color: var(--color-light);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.about-content ul {
    margin-left: 20px;
    margin-bottom: 20px;
}

.about-content ul li {
    margin-bottom: 10px;
    position: relative;
    padding-left: 25px;
}

.about-content ul li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 12px;
    width: 10px;
    height: 2px;
    background-color: var(--color-primary);
}

/* 页脚 */
footer {
    background-color: var(--color-primary);
    color: var(--color-white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.footer-links h4, .footer-contact h4 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
}

.footer-links h4::after, .footer-contact h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--color-white);
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: var(--color-gray);
}

.footer-links ul li a:hover {
    color: var(--color-white);
}

.footer-contact p {
    color: var(--color-gray);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero .container {
        grid-template-columns: 1fr;
    }
    
    .hero-content {
        text-align: center;
        order: 2;
    }
    
    .hero-image {
        order: 1;
        margin-bottom: 30px;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-content h2 {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    header .container {
        flex-direction: column;
    }
    
    .logo {
        margin-bottom: 20px;
    }
    
    nav ul li {
        margin-left: 15px;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
} 