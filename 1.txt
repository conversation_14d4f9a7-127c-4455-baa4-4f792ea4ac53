你是一个高级后端开发助手，擅长设计和实现高性能、安全的RESTful API。请为我开发一个邀请码核销系统的API，具体要求如下：

1. **功能需求**：
   - 系统通过RESTful API提供邀请码核销功能。
   - 用户提交唯一邀请码和身份信息（如用户ID），核销与该身份绑定的邀请码。
   - 验证邀请码的有效性（是否存在、未使用、未过期、身份匹配）。
   - 返回清晰的成功/失败响应，包含错误码和消息。
   - 支持高并发场景，防止重复核销。

2. **技术要求**：
   - 语言：Python（使用Flask框架）
   - 数据库：使用db文件存储在本地
   - 安全：实现身份验证（如JWT或API Key），防止未授权访问，限制请求频率。
   - 性能：使用缓存和数据库索引优化查询效率。
   - 错误处理：返回标准化的JSON响应（如{"error": "邀请码不存在", "code": 404}）。

3. **核心功能**：
   - **生成邀请码**：生成唯一邀请码（8位，基于UUID或其他算法），绑定特定用户身份，设置有效期（如7天）。
   - **核销邀请码**：API端点`POST /api/redeem`，验证邀请码和身份，更新状态，记录核销时间。
   - **查询邀请码（可选）**：API端点`GET /api/codes`，返回邀请码状态。

4. **代码要求**：
   - 提供完整的代码示例，包括：
     - 数据库模型
     - API路由和逻辑。
     - 身份验证和错误处理。
     - Redis缓存集成（可选）。
   - 代码结构清晰，包含注释，遵循PEP 8规范（或相应语言的编码规范）。
   - 提供简单的部署说明（如Docker配置）。

5. **输出格式**：
   - 返回完整的代码文件（例如`app.py`），包含所有依赖项。
   - 附带API测试用例（cURL命令或Postman脚本）。
   - 提供简要的说明文档，描述每个模块的功能和使用方法。

6. **额外要求**：
   - 如果有性能优化建议（如分布式锁、批量核销），请在注释或文档中说明。
   - 如果遇到模糊需求，请提出合理假设并记录。
   - 优先考虑简洁性和可维护性。

请根据以上要求生成代码，并确保逻辑正确、性能高效、安全可靠。如果需要澄清任何细节，请在响应中提出问题或假设。