<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>100%宽度测试 - 邀请码核销系统</title>
    <link rel="stylesheet" href="static/css/modern-console.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        /* 调试样式 - 显示边界 */
        .debug {
            border: 2px solid red !important;
        }
        .debug-green {
            border: 2px solid green !important;
        }
        .debug-blue {
            border: 2px solid blue !important;
        }
        
        /* 测试用的全宽度背景 */
        .test-fullwidth {
            background: linear-gradient(90deg, #ff0000 0%, #00ff00 50%, #0000ff 100%);
            height: 20px;
            margin: 10px 0;
        }
    </style>
</head>
<body class="debug">
    <div class="dashboard debug-green">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>邀请码核销系统</h2>
                <p>宽度测试</p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="#dashboard" class="nav-link active">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="7" height="7"></rect>
                                <rect x="14" y="3" width="7" height="7"></rect>
                                <rect x="14" y="14" width="7" height="7"></rect>
                                <rect x="3" y="14" width="7" height="7"></rect>
                            </svg>
                            仪表盘
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <p>&copy; 2024 宽度测试</p>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content debug-blue">
            <!-- 内容头部 -->
            <div class="content-header">
                <h1>100%宽度测试</h1>
                <div class="user-info">
                    <span>测试用户</span>
                </div>
            </div>

            <!-- 内容主体 -->
            <div class="content-body">
                <!-- 测试全宽度元素 -->
                <div class="test-fullwidth"></div>
                
                <div class="card">
                    <div class="card-header">
                        <h2>宽度测试说明</h2>
                    </div>
                    <div class="card-body">
                        <p>这个页面用于测试布局是否能够100%填充屏幕宽度。</p>
                        <ul style="margin: 20px 0; padding-left: 20px;">
                            <li><strong>红色边框</strong>: body元素</li>
                            <li><strong>绿色边框</strong>: dashboard容器</li>
                            <li><strong>蓝色边框</strong>: main-content区域</li>
                            <li><strong>彩色条</strong>: 测试全宽度元素</li>
                        </ul>
                        <p>如果修复成功，你应该看到：</p>
                        <ul style="margin: 20px 0; padding-left: 20px;">
                            <li>所有边框都应该贴合浏览器边缘</li>
                            <li>彩色条应该从左到右完全填充内容区域</li>
                            <li>没有左右留白</li>
                        </ul>
                    </div>
                </div>

                <!-- 统计卡片测试 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>测试1</h3>
                        <div class="stat-value">100%</div>
                        <div class="stat-label">宽度填充</div>
                    </div>
                    <div class="stat-card">
                        <h3>测试2</h3>
                        <div class="stat-value">OK</div>
                        <div class="stat-label">布局正常</div>
                    </div>
                    <div class="stat-card">
                        <h3>测试3</h3>
                        <div class="stat-value">✓</div>
                        <div class="stat-label">响应式</div>
                    </div>
                </div>

                <!-- 另一个测试全宽度元素 -->
                <div class="test-fullwidth"></div>

                <!-- 表格测试 -->
                <div class="card">
                    <div class="card-header">
                        <h2>表格宽度测试</h2>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>列1</th>
                                        <th>列2</th>
                                        <th>列3</th>
                                        <th>列4</th>
                                        <th>列5</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>数据1</td>
                                        <td>数据2</td>
                                        <td>数据3</td>
                                        <td>数据4</td>
                                        <td>数据5</td>
                                    </tr>
                                    <tr>
                                        <td>测试A</td>
                                        <td>测试B</td>
                                        <td>测试C</td>
                                        <td>测试D</td>
                                        <td>测试E</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 最后一个测试全宽度元素 -->
                <div class="test-fullwidth"></div>
            </div>
        </div>
    </div>

    <script>
        // 显示当前窗口宽度信息
        function showWindowInfo() {
            const info = document.createElement('div');
            info.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                z-index: 9999;
            `;
            info.innerHTML = `
                窗口宽度: ${window.innerWidth}px<br>
                文档宽度: ${document.documentElement.clientWidth}px<br>
                body宽度: ${document.body.clientWidth}px
            `;
            document.body.appendChild(info);
            
            // 5秒后移除
            setTimeout(() => {
                document.body.removeChild(info);
            }, 5000);
        }

        // 页面加载完成后显示信息
        window.addEventListener('load', showWindowInfo);
        
        // 窗口大小改变时也显示信息
        window.addEventListener('resize', showWindowInfo);
    </script>
</body>
</html>
