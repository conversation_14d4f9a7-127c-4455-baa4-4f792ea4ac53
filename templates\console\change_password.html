<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - 邀请码系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .password-form-container {
            max-width: 500px;
            margin: 100px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .password-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .password-form {
            margin-top: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .btn-submit {
            width: 100%;
            padding: 12px;
            background-color: #0d6efd;
            border: none;
            color: white;
            font-weight: 600;
            border-radius: 5px;
            margin-top: 10px;
        }
        .btn-submit:hover {
            background-color: #0b5ed7;
        }
        .password-requirements {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        .password-requirements ul {
            padding-left: 20px;
            margin-bottom: 0;
        }
        .password-strength {
            height: 5px;
            margin-top: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .password-feedback {
            font-size: 0.85rem;
            margin-top: 5px;
        }
        .force-change-alert {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="password-form-container">
            <h2 class="password-title">修改密码</h2>
            
            <div id="forceChangeAlert" class="alert alert-warning force-change-alert" style="display: none;">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                您需要修改密码才能继续使用系统
            </div>
            
            <div id="alertContainer"></div>
            
            <form id="passwordForm" class="password-form">
                <div class="form-group">
                    <label for="currentPassword" class="form-label">当前密码</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="currentPassword" required>
                        <button class="btn btn-outline toggle-password" type="button" data-target="currentPassword">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="newPassword" class="form-label">新密码</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="newPassword" required>
                        <button class="btn btn-outline toggle-password" type="button" data-target="newPassword">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength" id="passwordStrength"></div>
                    <div class="password-feedback text-muted" id="passwordFeedback"></div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword" class="form-label">确认新密码</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="confirmPassword" required>
                        <button class="btn btn-outline toggle-password" type="button" data-target="confirmPassword">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback" id="confirmFeedback">密码不匹配</div>
                </div>
                
                <div class="password-requirements">
                    <h6>密码要求：</h6>
                    <ul id="passwordRequirements">
                        <li id="req-length">长度至少为 <span id="minLength">10</span> 个字符</li>
                        <li id="req-uppercase">包含至少一个大写字母</li>
                        <li id="req-number">包含至少一个数字</li>
                        <li id="req-special">包含至少一个特殊字符</li>
                    </ul>
                </div>
                
                <button type="submit" class="btn btn-primary" id="submitBtn">修改密码</button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户状态
            const userDataStr = sessionStorage.getItem('user');
            if (userDataStr) {
                const userData = JSON.parse(userDataStr);
                if (userData.force_password_change) {
                    document.getElementById('forceChangeAlert').style.display = 'block';
                }
            }
            
            // 获取配置信息
            fetch('/api/auth/password-requirements')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const requirements = data.data;
                        document.getElementById('minLength').textContent = requirements.min_length || 10;
                        
                        if (!requirements.require_uppercase) {
                            document.getElementById('req-uppercase').style.display = 'none';
                        }
                        
                        if (!requirements.require_numbers) {
                            document.getElementById('req-number').style.display = 'none';
                        }
                        
                        if (!requirements.require_special) {
                            document.getElementById('req-special').style.display = 'none';
                        }
                    }
                })
                .catch(error => console.error('获取密码要求失败:', error));
            
            // 密码强度检查
            const newPasswordInput = document.getElementById('newPassword');
            const passwordStrength = document.getElementById('passwordStrength');
            const passwordFeedback = document.getElementById('passwordFeedback');
            
            newPasswordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                let feedback = '';
                
                if (password.length >= 8) {
                    strength += 25;
                }
                
                if (password.match(/[A-Z]/)) {
                    strength += 25;
                }
                
                if (password.match(/[0-9]/)) {
                    strength += 25;
                }
                
                if (password.match(/[^A-Za-z0-9]/)) {
                    strength += 25;
                }
                
                passwordStrength.style.width = strength + '%';
                
                if (strength <= 25) {
                    passwordStrength.style.backgroundColor = '#dc3545';
                    feedback = '密码强度：弱';
                } else if (strength <= 50) {
                    passwordStrength.style.backgroundColor = '#ffc107';
                    feedback = '密码强度：中';
                } else if (strength <= 75) {
                    passwordStrength.style.backgroundColor = '#0dcaf0';
                    feedback = '密码强度：良好';
                } else {
                    passwordStrength.style.backgroundColor = '#198754';
                    feedback = '密码强度：强';
                }
                
                passwordFeedback.textContent = feedback;
                
                // 检查密码确认
                const confirmPassword = document.getElementById('confirmPassword').value;
                if (confirmPassword && confirmPassword !== password) {
                    document.getElementById('confirmPassword').classList.add('is-invalid');
                    document.getElementById('confirmFeedback').style.display = 'block';
                } else if (confirmPassword) {
                    document.getElementById('confirmPassword').classList.remove('is-invalid');
                    document.getElementById('confirmFeedback').style.display = 'none';
                }
            });
            
            // 密码确认检查
            document.getElementById('confirmPassword').addEventListener('input', function() {
                const password = document.getElementById('newPassword').value;
                const confirmPassword = this.value;
                
                if (confirmPassword && confirmPassword !== password) {
                    this.classList.add('is-invalid');
                    document.getElementById('confirmFeedback').style.display = 'block';
                } else {
                    this.classList.remove('is-invalid');
                    document.getElementById('confirmFeedback').style.display = 'none';
                }
            });
            
            // 显示/隐藏密码
            document.querySelectorAll('.toggle-password').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const input = document.getElementById(targetId);
                    const icon = this.querySelector('i');
                    
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('bi-eye');
                        icon.classList.add('bi-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('bi-eye-slash');
                        icon.classList.add('bi-eye');
                    }
                });
            });
            
            // 表单提交
            document.getElementById('passwordForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                if (newPassword !== confirmPassword) {
                    showAlert('danger', '新密码和确认密码不匹配');
                    return;
                }
                
                // 提交修改密码请求
                fetch('/api/auth/change_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        current_password: currentPassword,
                        new_password: newPassword
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', '密码修改成功，即将跳转到控制台...');
                        
                        // 更新用户信息
                        const userDataStr = sessionStorage.getItem('user');
                        if (userDataStr) {
                            const userData = JSON.parse(userDataStr);
                            userData.force_password_change = false;
                            sessionStorage.setItem('user', JSON.stringify(userData));
                        }
                        
                        // 延迟跳转
                        setTimeout(() => {
                            window.location.href = '/dashboard';
                        }, 2000);
                    } else {
                        showAlert('danger', data.message || '密码修改失败');
                    }
                })
                .catch(error => {
                    console.error('修改密码请求失败:', error);
                    showAlert('danger', '请求失败，请稍后重试');
                });
            });
            
            // 显示提示信息
            function showAlert(type, message) {
                const alertContainer = document.getElementById('alertContainer');
                const alert = document.createElement('div');
                alert.className = `alert alert-${type} alert-dismissible fade show`;
                alert.role = 'alert';
                
                alert.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                
                alertContainer.innerHTML = '';
                alertContainer.appendChild(alert);
                
                // 自动关闭
                if (type !== 'danger') {
                    setTimeout(() => {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }, 5000);
                }
            }
        });
    </script>
</body>
</html> 