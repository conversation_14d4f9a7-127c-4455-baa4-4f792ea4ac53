<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API批量生成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .codes-list {
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        .code-item {
            padding: 8px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Key批量生成邀请码测试</h1>
        
        <div class="form-group">
            <label for="token">访问令牌 (JWT Token)</label>
            <input type="text" id="token" placeholder="请输入JWT Token">
            <small>从浏览器开发者工具的localStorage中获取access_token</small>
        </div>
        
        <div class="form-group">
            <label for="api-key-id">API Key ID</label>
            <input type="number" id="api-key-id" placeholder="请输入API Key ID" value="1">
        </div>
        
        <div class="form-group">
            <label for="count">生成数量</label>
            <input type="number" id="count" min="1" max="100" value="5">
        </div>
        
        <div class="form-group">
            <label for="expiry-days">有效期（天）</label>
            <input type="number" id="expiry-days" min="1" max="365" value="7">
        </div>
        
        <button class="btn" onclick="testBatchGenerate()">测试批量生成</button>
        
        <div id="result" class="result">
            <div id="result-message"></div>
            <div id="codes-list" class="codes-list" style="display: none;"></div>
        </div>
    </div>

    <script>
        function testBatchGenerate() {
            const token = document.getElementById('token').value.trim();
            const keyId = document.getElementById('api-key-id').value;
            const count = parseInt(document.getElementById('count').value);
            const expiryDays = parseInt(document.getElementById('expiry-days').value);
            
            if (!token) {
                showResult('请输入JWT Token', 'error');
                return;
            }
            
            if (!keyId || count < 1 || count > 100 || expiryDays < 1 || expiryDays > 365) {
                showResult('请检查输入参数', 'error');
                return;
            }
            
            const btn = document.querySelector('.btn');
            btn.disabled = true;
            btn.textContent = '生成中...';
            
            fetch(`http://127.0.0.1:5000/api/keys/${keyId}/codes/batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    count: count,
                    expiry_days: expiryDays
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(`成功生成 ${data.data.count} 个邀请码`, 'success');
                    displayCodes(data.data.codes);
                } else {
                    showResult(`生成失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                showResult(`请求失败: ${error.message}`, 'error');
            })
            .finally(() => {
                btn.disabled = false;
                btn.textContent = '测试批量生成';
            });
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            const resultMessage = document.getElementById('result-message');
            
            result.className = `result ${type}`;
            result.style.display = 'block';
            resultMessage.textContent = message;
            
            if (type === 'error') {
                document.getElementById('codes-list').style.display = 'none';
            }
        }
        
        function displayCodes(codes) {
            const codesList = document.getElementById('codes-list');
            codesList.innerHTML = '';
            
            codes.forEach(codeObj => {
                const code = codeObj.code || codeObj;
                const codeItem = document.createElement('div');
                codeItem.className = 'code-item';
                codeItem.innerHTML = `
                    <span>${code}</span>
                    <button class="copy-btn" onclick="copyCode('${code}')">复制</button>
                `;
                codesList.appendChild(codeItem);
            });
            
            codesList.style.display = 'block';
        }
        
        function copyCode(code) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(code).then(() => {
                    alert(`已复制: ${code}`);
                });
            } else {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert(`已复制: ${code}`);
            }
        }
        
        // 页面加载时尝试从localStorage获取token
        window.addEventListener('load', function() {
            const token = localStorage.getItem('access_token');
            if (token) {
                document.getElementById('token').value = token;
            }
        });
    </script>
</body>
</html>
