"""
管理员服务模块 - 提供管理员账户管理相关功能
"""

import random
import string
import logging
from datetime import datetime
from werkzeug.security import generate_password_hash
from flask import current_app

from models import db, User, Role

logger = logging.getLogger(__name__)

def generate_secure_password(length=12):
    """
    生成安全的随机密码
    
    Args:
        length: 密码长度，默认12位
        
    Returns:
        str: 生成的随机密码
    """
    # 确保包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*()-_=+[]{}|;:,.<>?"
    
    # 确保每种字符至少出现一次
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special)
    ]
    
    # 填充剩余长度
    remaining_length = length - len(password)
    all_chars = lowercase + uppercase + digits + special
    password.extend(random.choice(all_chars) for _ in range(remaining_length))
    
    # 打乱密码字符顺序
    random.shuffle(password)
    
    return ''.join(password)

def create_admin_user(username, email, password=None):
    """
    创建管理员用户（简化版）
    
    Args:
        username: 用户名
        email: 电子邮箱
        password: 密码，如果为None则自动生成
        
    Returns:
        tuple: (成功标志, 用户对象或错误消息)
    """
    try:
        # 检查用户是否已存在
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            return False, f"用户名 '{username}' 已存在"
        
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return False, f"邮箱 '{email}' 已存在"
        
        # 如果没有提供密码，生成一个安全的随机密码
        generated_password = False
        if not password:
            password = generate_secure_password()
            generated_password = True
        
        # 创建用户
        user = User(
            username=username,
            email=email,
            is_admin=True  # 设置为管理员
        )
        user.password = password
        
        # 添加基本角色（保持与现有系统的兼容性）
        role = Role.query.filter_by(name='admin').first()
        if role:
            user.roles.append(role)
        
        db.session.add(user)
        db.session.commit()
        
        result_message = {
            'user': user,
            'password': password if generated_password else None,
            'generated_password': generated_password
        }
        
        return True, result_message
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建管理员用户失败: {str(e)}")
        return False, f"创建管理员用户失败: {str(e)}"

def create_default_admin():
    """
    创建默认管理员账户（简化版）
    
    Returns:
        bool: 是否成功创建
    """
    if not current_app:
        logger.warning("没有活动的应用上下文，无法创建默认管理员")
        return False
    
    username = current_app.config.get('ADMIN_USERNAME')
    email = current_app.config.get('ADMIN_EMAIL')
    password = current_app.config.get('ADMIN_PASSWORD')
    
    # 检查管理员是否已存在
    admin = User.query.filter_by(username=username).first()
    if admin:
        logger.info(f"默认管理员账户 '{username}' 已存在")
        return False
    
    # 创建默认管理员
    success, result = create_admin_user(
        username=username,
        email=email,
        password=password
    )
    
    if success:
        user = result['user']
        logger.info(f"默认管理员账户创建成功！用户名: {user.username}, 邮箱: {user.email}")
        return True
    else:
        logger.error(f"创建默认管理员失败: {result}")
        return False

def validate_admin_password(password):
    """
    验证管理员密码是否符合安全要求
    
    Args:
        password: 要验证的密码
        
    Returns:
        tuple: (是否有效, 错误消息)
    """
    if not current_app:
        return True, None
    
    # 检查密码长度
    min_length = current_app.config.get('ADMIN_PASSWORD_MIN_LENGTH', 8)
    if len(password) < min_length:
        return False, f"密码长度至少为{min_length}个字符"
    
    # 检查是否包含大写字母
    if current_app.config.get('ADMIN_PASSWORD_REQUIRE_UPPERCASE', False):
        if not any(c.isupper() for c in password):
            return False, "密码必须包含至少一个大写字母"
    
    # 检查是否包含数字
    if current_app.config.get('ADMIN_PASSWORD_REQUIRE_NUMBERS', False):
        if not any(c.isdigit() for c in password):
            return False, "密码必须包含至少一个数字"
    
    # 检查是否包含特殊字符
    if current_app.config.get('ADMIN_PASSWORD_REQUIRE_SPECIAL', False):
        special_chars = "!@#$%^&*()-_=+[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            return False, "密码必须包含至少一个特殊字符"
    
    return True, None

def change_admin_password(user_id, new_password):
    """
    修改管理员密码
    
    Args:
        user_id: 用户ID
        new_password: 新密码
        
    Returns:
        tuple: (是否成功, 错误消息)
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return False, "用户不存在"
        
        # 检查是否为管理员
        if not user.is_admin:
            return False, "只能修改管理员账户的密码"
        
        # 验证密码强度
        is_valid, error_msg = validate_admin_password(new_password)
        if not is_valid:
            return False, error_msg
        
        # 更新密码
        user.password = new_password
        db.session.commit()
        
        return True, "密码修改成功"
    except Exception as e:
        db.session.rollback()
        logger.error(f"修改管理员密码失败: {str(e)}")
        return False, f"修改密码失败: {str(e)}" 