<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>核销记录删除功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .table tr:hover {
            background: #f5f5f5;
        }
        .actions {
            display: flex;
            gap: 5px;
        }
        .actions button {
            padding: 4px 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>核销记录删除功能测试</h1>
        
        <div class="form-group">
            <label for="token">访问令牌 (JWT Token)</label>
            <input type="text" id="token" placeholder="请输入JWT Token">
            <small>从浏览器开发者工具的localStorage中获取access_token</small>
        </div>
        
        <button class="btn" onclick="loadRedeemRecords()">加载核销记录</button>
        <button class="btn danger" onclick="testBatchDelete()" disabled id="batch-delete-btn">批量删除选中</button>
        
        <div id="result" class="result">
            <div id="result-message"></div>
        </div>
    </div>

    <div class="container">
        <h2>核销记录列表</h2>
        <table class="table" id="redeem-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all"></th>
                    <th>邀请码</th>
                    <th>核销时间</th>
                    <th>核销方式</th>
                    <th>创建者</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="redeem-table-body">
                <tr>
                    <td colspan="6" style="text-align: center;">请先加载数据</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        let redeemRecords = [];
        
        function loadRedeemRecords() {
            const token = document.getElementById('token').value.trim();
            
            if (!token) {
                showResult('请输入JWT Token', 'error');
                return;
            }
            
            fetch('http://127.0.0.1:5000/api/invite/codes?status=used&per_page=50', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    redeemRecords = data.data.codes;
                    displayRedeemRecords(redeemRecords);
                    showResult(`加载成功，共 ${redeemRecords.length} 条核销记录`, 'success');
                } else {
                    showResult(`加载失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('加载失败:', error);
                showResult(`加载失败: ${error.message}`, 'error');
            });
        }
        
        function displayRedeemRecords(records) {
            const tbody = document.getElementById('redeem-table-body');
            
            if (records.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无核销记录</td></tr>';
                return;
            }
            
            let html = '';
            records.forEach(record => {
                const redeemMethod = record.used_by_id ? '用户核销' : 'API核销';
                const creatorName = record.creator_name || '未知';
                const redeemTime = new Date(record.redeemed_at).toLocaleString();
                
                html += `
                    <tr>
                        <td><input type="checkbox" class="record-checkbox" value="${record.id}"></td>
                        <td>${record.code}</td>
                        <td>${redeemTime}</td>
                        <td>${redeemMethod}</td>
                        <td>${creatorName}</td>
                        <td class="actions">
                            <button class="btn danger" onclick="deleteRecord(${record.id}, '${record.code}')">删除</button>
                        </td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
            
            // 添加复选框事件监听
            updateBatchDeleteButton();
            document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateBatchDeleteButton);
            });
        }
        
        function deleteRecord(recordId, code) {
            if (!confirm(`确定要删除核销记录 "${code}" 吗？\n删除后邀请码将重置为未使用状态。`)) {
                return;
            }
            
            const token = document.getElementById('token').value.trim();
            
            fetch(`http://127.0.0.1:5000/api/invite/redeem/${recordId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('核销记录删除成功', 'success');
                    loadRedeemRecords(); // 重新加载数据
                } else {
                    showResult(`删除失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                showResult(`删除失败: ${error.message}`, 'error');
            });
        }
        
        function testBatchDelete() {
            const checkboxes = document.querySelectorAll('.record-checkbox:checked');
            const recordIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
            
            if (recordIds.length === 0) {
                showResult('请选择要删除的记录', 'error');
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${recordIds.length} 个核销记录吗？\n删除后对应的邀请码将重置为未使用状态。`)) {
                return;
            }
            
            const token = document.getElementById('token').value.trim();
            
            fetch('http://127.0.0.1:5000/api/invite/redeem/batch', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ code_ids: recordIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(data.message, 'success');
                    loadRedeemRecords(); // 重新加载数据
                } else {
                    showResult(`批量删除失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                showResult(`批量删除失败: ${error.message}`, 'error');
            });
        }
        
        function updateBatchDeleteButton() {
            const checkboxes = document.querySelectorAll('.record-checkbox:checked');
            const batchBtn = document.getElementById('batch-delete-btn');
            batchBtn.disabled = checkboxes.length === 0;
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            const resultMessage = document.getElementById('result-message');
            
            result.className = `result ${type}`;
            result.style.display = 'block';
            resultMessage.textContent = message;
        }
        
        // 全选功能
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.record-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBatchDeleteButton();
        });
        
        // 页面加载时尝试从localStorage获取token
        window.addEventListener('load', function() {
            const token = localStorage.getItem('access_token');
            if (token) {
                document.getElementById('token').value = token;
            }
        });
    </script>
</body>
</html>
