# API Key批量生成邀请码功能指南

## 功能概述

API Key批量生成功能允许用户通过指定的API Key一次性生成多个邀请码，提高了邀请码管理的效率。该功能采用现代化的模态弹窗设计，提供直观的用户界面和便捷的操作体验。

## 功能特点

### 🎯 核心功能
- **批量生成**: 支持一次性生成1-100个邀请码
- **自定义有效期**: 可设置1-365天的有效期
- **API Key关联**: 生成的邀请码自动关联到指定的API Key
- **实时反馈**: 生成过程中显示加载状态和进度

### 📋 结果管理
- **清晰展示**: 在模态框中列表形式展示所有生成的邀请码
- **单个复制**: 每个邀请码都有独立的复制按钮
- **批量复制**: 一键复制所有生成的邀请码
- **文件下载**: 支持将邀请码列表下载为txt文件
- **自动下载**: 可选择生成后自动下载文件

### 🎨 用户体验
- **现代化设计**: 采用现代化的模态框和表单设计
- **响应式布局**: 完美适配桌面端和移动端
- **表单验证**: 实时验证用户输入，确保数据有效性
- **操作反馈**: 提供清晰的成功/错误提示消息

## 使用方法

### 1. 打开批量生成模态框
1. 在API Key管理页面找到目标API Key
2. 点击对应行的"批量生成"按钮
3. 系统会打开批量生成模态框

### 2. 设置生成参数
1. **API Key信息**: 自动显示选中的API Key名称和密钥
2. **生成数量**: 输入要生成的邀请码数量（1-100）
3. **有效期**: 设置邀请码的有效期天数（1-365）
4. **自动下载**: 选择是否在生成后自动下载邀请码列表

### 3. 执行生成操作
1. 点击"开始生成"按钮
2. 系统显示生成进度（加载动画）
3. 生成完成后自动显示结果区域

### 4. 管理生成结果
1. **查看邀请码**: 在结果列表中查看所有生成的邀请码
2. **复制单个**: 点击每个邀请码旁的"复制"按钮
3. **复制全部**: 点击"复制全部"按钮复制所有邀请码
4. **下载列表**: 点击"下载列表"按钮下载txt文件
5. **关闭结果**: 点击"关闭"按钮隐藏结果区域

## 技术实现

### 前端组件
```html
<!-- 模态框结构 -->
<div id="api-batch-generate-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">...</div>
        <div class="modal-body">
            <form id="api-batch-generate-form">...</form>
            <div id="api-batch-result" class="batch-result">...</div>
        </div>
    </div>
</div>
```

### JavaScript功能
```javascript
// 主要功能函数
showApiBatchGenerateModal(apiKeyId, apiKeyName, apiKey)  // 显示模态框
closeApiBatchGenerateModal()                            // 关闭模态框
handleApiBatchGenerate(event)                           // 处理生成请求
showApiBatchResult(codes, apiKeyName, autoDownload)     // 显示结果
copyApiBatchCodes()                                     // 复制所有邀请码
downloadApiBatchCodes()                                 // 下载邀请码列表
```

### CSS样式类
```css
.modal                    // 模态框容器
.modal-content           // 模态框内容
.batch-result           // 批量结果容器
.batch-result-header    // 结果头部
.batch-codes-container  // 邀请码列表容器
.batch-code-item        // 单个邀请码项
.batch-code-text        // 邀请码文本
```

## API接口设计

### 批量生成接口
```http
POST /api/invite/batch-generate
Headers: X-API-Key: your-api-key
Content-Type: application/json

{
    "count": 10,
    "expiry_days": 7
}
```

### 响应格式
```json
{
    "success": true,
    "message": "批量生成成功",
    "data": {
        "codes": [
            {
                "code": "ABCD1234",
                "expires_at": "2024-01-22T10:30:00Z"
            }
        ],
        "count": 10,
        "api_key_id": 1
    }
}
```

## 文件结构

```
templates/console/
└── dashboard.html          # 主模板文件（包含模态框）

static/css/
└── modern-console.css      # 样式文件（包含批量结果样式）

demo-api-batch.html         # 功能演示页面
API_BATCH_GENERATE_GUIDE.md # 本功能指南
```

## 浏览器兼容性

- ✅ Chrome 88+
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 88+

## 安全考虑

1. **权限验证**: 确保只有有效的API Key才能执行批量生成
2. **频率限制**: 建议对批量生成操作进行频率限制
3. **数量限制**: 单次生成数量限制在100个以内
4. **有效期限制**: 有效期限制在1-365天范围内

## 演示和测试

### 演示页面
打开 `demo-api-batch.html` 查看完整的功能演示：
- 包含完整的API Key列表
- 可以体验完整的批量生成流程
- 模拟真实的生成过程和结果展示

### 测试要点
1. **表单验证**: 测试各种无效输入的处理
2. **生成流程**: 测试完整的生成和结果展示流程
3. **复制功能**: 测试单个和批量复制功能
4. **下载功能**: 测试文件下载功能
5. **响应式**: 测试在不同设备上的显示效果

---

**开发完成时间**: 2024年1月
**功能状态**: 前端UI完成，等待后端API集成
**设计理念**: 现代化、用户友好、高效便捷
