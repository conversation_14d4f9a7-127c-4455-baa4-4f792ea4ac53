from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import desc
from datetime import datetime, timedelta

from models import db, InviteCode, User
from utils.helpers import api_response, bad_request, unauthorized, not_found, forbidden

# 创建邀请码蓝图
invite_bp = Blueprint('invite', __name__)

@invite_bp.route('/generate', methods=['POST'])
@jwt_required()
def generate_invite():
    """生成邀请码"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return not_found("用户不存在")
    
    # 可选：限制非管理员用户生成邀请码的数量
    if not user.is_admin:
        active_codes = InviteCode.query.filter_by(
            creator_id=user_id, 
            is_used=False
        ).filter(
            InviteCode.expires_at > datetime.utcnow()
        ).count()
        
        if active_codes >= 5:  # 假设每个普通用户最多可以有5个未使用的邀请码
            return forbidden("您已达到未使用邀请码的上限")
    
    # 从请求中获取有效期（可选）
    data = request.get_json() or {}
    expiry_days = data.get('expiry_days', current_app.config['INVITE_CODE_EXPIRY_DAYS'])
    
    # 创建邀请码
    invite = InviteCode.create_code(user_id, expiry_days)
    
    db.session.add(invite)
    db.session.commit()
    
    return api_response(
        data=invite.to_dict(),
        message="邀请码生成成功",
        status_code=201
    )

@invite_bp.route('/redeem', methods=['POST'])
@jwt_required()
def redeem_invite():
    """核销邀请码"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return not_found("用户不存在")
    
    data = request.get_json()
    
    if not data or 'code' not in data:
        return bad_request("请提供邀请码")
    
    invite_code = data['code']
    
    # 查找邀请码
    invite = InviteCode.query.filter_by(code=invite_code).first()
    
    if not invite:
        return not_found("邀请码不存在")
    
    # 验证邀请码
    if invite.is_used:
        return bad_request("邀请码已被使用")
    
    if invite.expires_at < datetime.utcnow():
        return bad_request("邀请码已过期")
    
    # 核销邀请码
    if not invite.redeem(user_id):
        return bad_request("邀请码无效")
    
    db.session.commit()
    
    return api_response(
        data=invite.to_dict(),
        message="邀请码核销成功"
    )

@invite_bp.route('/codes', methods=['GET'])
@jwt_required()
def get_invite_codes():
    """获取邀请码列表"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return not_found("用户不存在")
    
    # 查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 50)  # 限制每页最多50条
    status = request.args.get('status')  # 可选：all, used, unused, expired
    
    # 构建查询
    query = InviteCode.query
    
    # 非管理员只能查看自己的邀请码
    if not user.is_admin:
        query = query.filter(
            (InviteCode.creator_id == user_id) | 
            (InviteCode.used_by_id == user_id)
        )
    
    # 根据状态过滤
    if status == 'used':
        query = query.filter_by(is_used=True)
    elif status == 'unused':
        query = query.filter_by(is_used=False)
    elif status == 'expired':
        query = query.filter(InviteCode.expires_at < datetime.utcnow())
    
    # 分页
    pagination = query.order_by(desc(InviteCode.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    codes = pagination.items
    
    return api_response(
        data={
            'codes': [code.to_dict() for code in codes],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page
        },
        message="获取邀请码列表成功"
    )

@invite_bp.route('/codes/<code>', methods=['GET'])
@jwt_required()
def get_invite_code(code):
    """获取单个邀请码详情"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return not_found("用户不存在")
    
    # 查找邀请码
    invite = InviteCode.query.filter_by(code=code).first()
    
    if not invite:
        return not_found("邀请码不存在")
    
    # 非管理员只能查看自己的邀请码
    if not user.is_admin and invite.creator_id != user_id and invite.used_by_id != user_id:
        return forbidden("您无权查看此邀请码")
    
    return api_response(
        data=invite.to_dict(),
        message="获取邀请码详情成功"
    ) 