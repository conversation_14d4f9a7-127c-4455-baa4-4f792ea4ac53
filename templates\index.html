<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邀请码核销系统 - 简约高效的邀请管理平台</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-console.css') }}">
    <meta name="description" content="邀请码核销系统是一个简单高效的邀请码管理平台，提供邀请码生成、核销和管理功能。">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>邀请码核销系统</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="#features">功能</a></li>
                    <li><a href="#about">关于</a></li>
                    <li><a href="{{ url_for('login') }}" class="btn btn-outline">登录</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h2>简约高效的邀请码管理平台</h2>
                <p>生成、核销、管理邀请码，一站式解决方案</p>
                <a href="{{ url_for('login') }}" class="btn btn-primary">立即体验</a>
            </div>
            <div class="hero-image">
                <img src="{{ url_for('static', filename='img/hero.svg') }}" alt="邀请码系统示意图">
            </div>
        </div>
    </section>

    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">核心功能</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                            <path d="M2 17l10 5 10-5"></path>
                            <path d="M2 12l10 5 10-5"></path>
                        </svg>
                    </div>
                    <h3>生成邀请码</h3>
                    <p>快速生成唯一邀请码，设置有效期，绑定特定用户身份。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                    </div>
                    <h3>核销邀请码</h3>
                    <p>验证邀请码有效性，防止重复核销，记录核销时间。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                            <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                            <line x1="12" y1="22.08" x2="12" y2="12"></line>
                        </svg>
                    </div>
                    <h3>管理邀请码</h3>
                    <p>查看邀请码状态，追踪使用情况，管理有效期。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                    </div>
                    <h3>安全可靠</h3>
                    <p>JWT身份验证，请求频率限制，防止未授权访问。</p>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">关于系统</h2>
            <div class="about-content">
                <p>邀请码核销系统是一个基于Flask框架开发的高性能、安全的RESTful API系统。系统提供邀请码生成、核销和管理功能，适用于各类需要邀请码管理的场景。</p>
                <p>系统特点：</p>
                <ul>
                    <li>简约黑白风格设计，注重用户体验</li>
                    <li>高并发处理能力，防止重复核销</li>
                    <li>完善的安全机制，保护数据安全</li>
                    <li>灵活的API接口，方便集成到其他系统</li>
                </ul>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3>邀请码核销系统</h3>
                    <p>简约高效的邀请管理平台</p>
                </div>
                <div class="footer-links">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="#features">功能</a></li>
                        <li><a href="#about">关于</a></li>
                        <li><a href="{{ url_for('login') }}">登录</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h4>联系我们</h4>
                    <p>邮箱：<EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 邀请码核销系统. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html> 