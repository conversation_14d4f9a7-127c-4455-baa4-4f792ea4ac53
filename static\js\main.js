/**
 * 邀请码核销系统 - 主页面交互逻辑
 */

document.addEventListener('DOMContentLoaded', function() {
    // 平滑滚动
    setupSmoothScroll();
    
    // 检查登录状态
    checkLoginStatus();
});

/**
 * 设置平滑滚动
 */
function setupSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * 检查登录状态
 */
function checkLoginStatus() {
    // 尝试从 API 获取用户信息
    fetch('/api/auth/profile', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        }
        throw new Error('未登录');
    })
    .then(data => {
        if (data.success) {
            // 已登录，修改登录按钮为"进入控制台"
            const loginLinks = document.querySelectorAll('.btn-login');
            loginLinks.forEach(link => {
                link.textContent = '进入控制台';
                link.href = '/dashboard';
            });
            
            // 保存用户信息到 sessionStorage（不包含敏感信息）
            sessionStorage.setItem('user', JSON.stringify(data.data));
        }
    })
    .catch(error => {
        console.log('用户未登录或会话已过期');
    });
} 